#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Social Media Scraper...\n');

// Check Node.js version
const nodeVersion = process.version;
const requiredVersion = 'v18.0.0';
if (nodeVersion < requiredVersion) {
    console.error(`❌ Node.js ${requiredVersion} or higher is required. Current version: ${nodeVersion}`);
    process.exit(1);
}
console.log(`✅ Node.js version: ${nodeVersion}`);

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
    console.log('📝 Creating .env file from template...');
    const envExamplePath = path.join(process.cwd(), '.env.example');
    if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath);
        console.log('✅ .env file created. Please edit it with your configuration.');
    } else {
        console.error('❌ .env.example file not found');
    }
} else {
    console.log('✅ .env file already exists');
}

// Create necessary directories
const directories = [
    'logs',
    'data',
    'uploads',
    'config'
];

console.log('\n📁 Creating directories...');
directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    } else {
        console.log(`✅ Directory already exists: ${dir}`);
    }
});

// Check for required services
console.log('\n🔍 Checking required services...');

// Check MySQL
try {
    execSync('mysql --version', { stdio: 'ignore' });
    console.log('✅ MySQL is installed');
} catch (error) {
    console.log('⚠️  MySQL not found. Please install MySQL 8.0+');
}

// Check Redis
try {
    execSync('redis-cli --version', { stdio: 'ignore' });
    console.log('✅ Redis is installed');
} catch (error) {
    console.log('⚠️  Redis not found. Please install Redis 6.0+');
}

// Create sample configuration files
console.log('\n📄 Creating sample configuration files...');

// Create proxy configuration sample
const proxyConfigPath = path.join(process.cwd(), 'config', 'proxies.json');
if (!fs.existsSync(proxyConfigPath)) {
    const proxyConfig = {
        proxies: [
            {
                url: "http://proxy1.example.com:8080",
                auth: "username:password"
            },
            {
                url: "http://proxy2.example.com:8080",
                auth: "username:password"
            }
        ]
    };
    fs.writeFileSync(proxyConfigPath, JSON.stringify(proxyConfig, null, 2));
    console.log('✅ Created config/proxies.json');
}

// Create monitoring keywords sample
const keywordsPath = path.join(process.cwd(), 'config', 'monitoring_keywords.json');
if (!fs.existsSync(keywordsPath)) {
    const keywords = {
        high_priority: [
            "Malawi elections",
            "violence",
            "protest",
            "riot",
            "threat"
        ],
        medium_priority: [
            "Chakwera",
            "Mutharika",
            "Kabambe",
            "MCP",
            "DPP",
            "UTM"
        ],
        low_priority: [
            "Malawi politics",
            "democracy",
            "voting",
            "campaign"
        ]
    };
    fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));
    console.log('✅ Created config/monitoring_keywords.json');
}

// Create database setup script
const dbSetupPath = path.join(process.cwd(), 'scripts', 'setup-database.sql');
if (!fs.existsSync(dbSetupPath)) {
    const dbSetup = `-- Social Media Scraper Database Setup
-- Run this script to create the database and user

CREATE DATABASE IF NOT EXISTS social_media_monitoring 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Create user (optional - you can use root)
-- CREATE USER 'scraper_user'@'localhost' IDENTIFIED BY 'secure_password';
-- GRANT ALL PRIVILEGES ON social_media_monitoring.* TO 'scraper_user'@'localhost';
-- FLUSH PRIVILEGES;

USE social_media_monitoring;

-- The application will create tables automatically on first run
-- This script is just for manual database setup if needed

SELECT 'Database setup complete!' as message;
`;
    fs.writeFileSync(dbSetupPath, dbSetup);
    console.log('✅ Created scripts/setup-database.sql');
}

// Create systemd service file (for Linux deployment)
const servicePath = path.join(process.cwd(), 'scripts', 'social-media-scraper.service');
if (!fs.existsSync(servicePath)) {
    const serviceFile = `[Unit]
Description=Social Media Scraper
After=network.target mysql.service redis.service

[Service]
Type=simple
User=www-data
WorkingDirectory=${process.cwd()}
Environment=NODE_ENV=production
ExecStart=/usr/bin/node src/app.js
Restart=on-failure
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=social-media-scraper

[Install]
WantedBy=multi-user.target
`;
    fs.writeFileSync(servicePath, serviceFile);
    console.log('✅ Created scripts/social-media-scraper.service');
}

// Create Docker configuration
const dockerfilePath = path.join(process.cwd(), 'Dockerfile');
if (!fs.existsSync(dockerfilePath)) {
    const dockerfile = `FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs data uploads

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application
CMD ["npm", "start"]
`;
    fs.writeFileSync(dockerfilePath, dockerfile);
    console.log('✅ Created Dockerfile');
}

const dockerComposePath = path.join(process.cwd(), 'docker-compose.yml');
if (!fs.existsSync(dockerComposePath)) {
    const dockerCompose = `version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=mysql
      - REDIS_HOST=redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: social_media_monitoring
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
`;
    fs.writeFileSync(dockerComposePath, dockerCompose);
    console.log('✅ Created docker-compose.yml');
}

// Print next steps
console.log('\n🎉 Setup complete! Next steps:\n');
console.log('1. Edit .env file with your configuration:');
console.log('   - Database credentials');
console.log('   - Social media API keys');
console.log('   - Redis configuration\n');

console.log('2. Set up database:');
console.log('   mysql -u root -p < scripts/setup-database.sql\n');

console.log('3. Start Redis server:');
console.log('   redis-server\n');

console.log('4. Install dependencies (if not already done):');
console.log('   npm install\n');

console.log('5. Start the application:');
console.log('   npm run dev    # Development mode');
console.log('   npm start      # Production mode\n');

console.log('6. Test the API:');
console.log('   curl http://localhost:3000/api/health\n');

console.log('📚 Documentation:');
console.log('   - README.md for detailed setup instructions');
console.log('   - API documentation at http://localhost:3000/api/health');
console.log('   - Configuration files in ./config/\n');

console.log('⚠️  Important:');
console.log('   - Obtain API keys from Twitter, Facebook before starting');
console.log('   - Configure monitoring keywords in config/monitoring_keywords.json');
console.log('   - Review threat detection keywords in config/threat_keywords.json');
console.log('   - Set up proper security measures for production deployment\n');

console.log('✨ Happy monitoring!');
