const express = require('express');
const router = express.Router();
const { apiLogger } = require('../utils/logger');
const DatabaseManager = require('../database/DatabaseManager');
const CacheManager = require('../scrapers/storage/CacheManager');

// GET /api/health - Basic health check
router.get('/', async (req, res) => {
    try {
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development'
        };

        res.json(health);
    } catch (error) {
        apiLogger.error('Health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/detailed - Detailed health check
router.get('/detailed', async (req, res) => {
    try {
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            services: {}
        };

        // Check database connection
        try {
            await DatabaseManager.query('SELECT 1');
            health.services.database = {
                status: 'healthy',
                message: 'Database connection successful'
            };
        } catch (error) {
            health.services.database = {
                status: 'unhealthy',
                message: error.message
            };
            health.status = 'degraded';
        }

        // Check Redis connection
        try {
            await CacheManager.set('health_check', 'ok', 10);
            const result = await CacheManager.get('health_check');
            if (result === 'ok') {
                health.services.redis = {
                    status: 'healthy',
                    message: 'Redis connection successful'
                };
            } else {
                throw new Error('Redis read/write test failed');
            }
        } catch (error) {
            health.services.redis = {
                status: 'unhealthy',
                message: error.message
            };
            health.status = 'degraded';
        }

        // Check disk space
        try {
            const fs = require('fs');
            const stats = fs.statSync('.');
            health.services.filesystem = {
                status: 'healthy',
                message: 'Filesystem accessible'
            };
        } catch (error) {
            health.services.filesystem = {
                status: 'unhealthy',
                message: error.message
            };
            health.status = 'degraded';
        }

        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);

    } catch (error) {
        apiLogger.error('Detailed health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/database - Database health check
router.get('/database', async (req, res) => {
    try {
        const startTime = Date.now();
        
        // Test basic connection
        await DatabaseManager.query('SELECT 1 as test');
        
        // Test table access
        const postCount = await DatabaseManager.query('SELECT COUNT(*) as count FROM social_posts');
        const jobCount = await DatabaseManager.query('SELECT COUNT(*) as count FROM scraping_jobs');
        
        const responseTime = Date.now() - startTime;

        res.json({
            status: 'healthy',
            responseTime: `${responseTime}ms`,
            tables: {
                social_posts: postCount[0].count,
                scraping_jobs: jobCount[0].count
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error('Database health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/cache - Redis cache health check
router.get('/cache', async (req, res) => {
    try {
        const startTime = Date.now();
        
        // Test write
        await CacheManager.set('health_test', 'test_value', 60);
        
        // Test read
        const value = await CacheManager.get('health_test');
        
        // Test delete
        await CacheManager.del('health_test');
        
        const responseTime = Date.now() - startTime;

        if (value !== 'test_value') {
            throw new Error('Cache read/write test failed');
        }

        res.json({
            status: 'healthy',
            responseTime: `${responseTime}ms`,
            operations: ['set', 'get', 'del'],
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error('Cache health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/scrapers - Scraper health check
router.get('/scrapers', async (req, res) => {
    try {
        const scrapers = {
            twitter: {
                status: 'unknown',
                authenticated: false,
                lastActivity: null,
                stats: null
            },
            facebook: {
                status: 'unknown',
                authenticated: false,
                lastActivity: null,
                stats: null
            },
            tiktok: {
                status: 'unknown',
                authenticated: false,
                lastActivity: null,
                stats: null
            }
        };

        // Check recent scraping activity
        const recentActivity = await DatabaseManager.query(`
            SELECT platform, MAX(scraped_at) as last_activity, COUNT(*) as recent_posts
            FROM social_posts 
            WHERE scraped_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY platform
        `);

        for (const activity of recentActivity) {
            if (scrapers[activity.platform]) {
                scrapers[activity.platform].lastActivity = activity.last_activity;
                scrapers[activity.platform].recentPosts = activity.recent_posts;
                scrapers[activity.platform].status = activity.recent_posts > 0 ? 'active' : 'idle';
            }
        }

        // Check for any scrapers that haven't been active
        for (const [platform, scraper] of Object.entries(scrapers)) {
            if (!scraper.lastActivity) {
                scraper.status = 'inactive';
            }
        }

        res.json({
            status: 'healthy',
            scrapers,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error('Scraper health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/metrics - System metrics
router.get('/metrics', async (req, res) => {
    try {
        const metrics = {
            system: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                cpu: process.cpuUsage(),
                platform: process.platform,
                nodeVersion: process.version
            },
            database: {},
            cache: {},
            application: {}
        };

        // Database metrics
        try {
            const dbMetrics = await DatabaseManager.query(`
                SELECT 
                    (SELECT COUNT(*) FROM social_posts) as total_posts,
                    (SELECT COUNT(*) FROM social_posts WHERE scraped_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as posts_24h,
                    (SELECT COUNT(*) FROM scraping_jobs WHERE status = 'running') as running_jobs,
                    (SELECT COUNT(*) FROM scraping_jobs WHERE status = 'failed') as failed_jobs
            `);
            
            metrics.database = dbMetrics[0];
        } catch (error) {
            metrics.database.error = error.message;
        }

        // Cache metrics
        try {
            const cacheKeys = await CacheManager.getKeys('*');
            metrics.cache = {
                totalKeys: cacheKeys.length,
                keyPatterns: {
                    rateLimits: cacheKeys.filter(key => key.startsWith('rate_limit:')).length,
                    posts: cacheKeys.filter(key => key.startsWith('post:')).length,
                    users: cacheKeys.filter(key => key.startsWith('user_profile:')).length,
                    trending: cacheKeys.filter(key => key.startsWith('trending:')).length
                }
            };
        } catch (error) {
            metrics.cache.error = error.message;
        }

        // Application metrics
        metrics.application = {
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0',
            startTime: new Date(Date.now() - process.uptime() * 1000).toISOString()
        };

        res.json({
            status: 'healthy',
            metrics,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error('Metrics collection failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /api/health/logs - Recent log entries
router.get('/logs', async (req, res) => {
    try {
        const { level = 'error', limit = 50 } = req.query;
        const fs = require('fs').promises;
        const path = require('path');

        const logFile = level === 'error' 
            ? path.join(process.cwd(), 'logs', 'error.log')
            : path.join(process.cwd(), 'logs', 'combined.log');

        try {
            const logContent = await fs.readFile(logFile, 'utf8');
            const lines = logContent.split('\n').filter(line => line.trim());
            const recentLines = lines.slice(-parseInt(limit));

            const logs = recentLines.map(line => {
                try {
                    return JSON.parse(line);
                } catch {
                    return { message: line, timestamp: null };
                }
            });

            res.json({
                status: 'healthy',
                logs,
                count: logs.length,
                level,
                timestamp: new Date().toISOString()
            });

        } catch (fileError) {
            res.json({
                status: 'healthy',
                logs: [],
                count: 0,
                message: 'Log file not found or empty',
                timestamp: new Date().toISOString()
            });
        }

    } catch (error) {
        apiLogger.error('Log retrieval failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
