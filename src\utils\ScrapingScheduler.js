const cron = require('node-cron');
const schedule = require('node-schedule');
const { scraperLogger } = require('./logger');
const DatabaseManager = require('../database/DatabaseManager');
const TwitterScraper = require('../scrapers/platforms/TwitterScraper');
const FacebookScraper = require('../scrapers/platforms/FacebookScraper');
const TikTokScraper = require('../scrapers/platforms/TikTokScraper');

class ScrapingScheduler {
    constructor() {
        this.logger = scraperLogger;
        this.jobs = new Map();
        this.scrapers = {
            twitter: new TwitterScraper(),
            facebook: new FacebookScraper(),
            tiktok: new TikTokScraper()
        };
        this.isInitialized = false;
        
        // Default monitoring keywords for Malawi
        this.monitoringKeywords = [
            'Malawi elections',
            'Chakwera',
            'Mutharika',
            'Kabambe',
            'MCP',
            'DPP',
            'UTM',
            'Malawi politics',
            'Lilong<PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Malawi government',
            'Malawi democracy'
        ];

        // High-priority keywords that need frequent monitoring
        this.highPriorityKeywords = [
            'Malawi elections',
            'violence',
            'protest',
            'riot',
            'threat'
        ];

        // Accounts to monitor
        this.monitoredAccounts = {
            twitter: [
                'MalawiGovt',
                'MEC_Malawi',
                'MalawiPolice'
            ],
            facebook: [
                'MalawiGovernment',
                'MalawiElectoralCommission'
            ],
            tiktok: [
                // Add relevant TikTok accounts
            ]
        };
    }

    async initialize() {
        try {
            this.logger.info('Initializing scraping scheduler');

            // Initialize scrapers
            for (const [platform, scraper] of Object.entries(this.scrapers)) {
                try {
                    await scraper.initialize();
                    this.logger.info(`${platform} scraper initialized`);
                } catch (error) {
                    this.logger.error(`Failed to initialize ${platform} scraper:`, error);
                }
            }

            // Schedule monitoring jobs
            this.scheduleKeywordMonitoring();
            this.scheduleAccountMonitoring();
            this.scheduleTrendingAnalysis();
            this.scheduleHealthChecks();

            this.isInitialized = true;
            this.logger.info('Scraping scheduler initialized successfully');

        } catch (error) {
            this.logger.error('Failed to initialize scraping scheduler:', error);
            throw error;
        }
    }

    scheduleKeywordMonitoring() {
        // High-priority keywords every 15 minutes
        const highPriorityJob = cron.schedule('*/15 * * * *', async () => {
            await this.scrapeKeywords(this.highPriorityKeywords, 'high-priority');
        }, {
            scheduled: false
        });

        // All keywords every hour
        const regularJob = cron.schedule('0 * * * *', async () => {
            await this.scrapeKeywords(this.monitoringKeywords, 'regular');
        }, {
            scheduled: false
        });

        this.jobs.set('high-priority-keywords', highPriorityJob);
        this.jobs.set('regular-keywords', regularJob);

        highPriorityJob.start();
        regularJob.start();

        this.logger.info('Keyword monitoring scheduled');
    }

    scheduleAccountMonitoring() {
        // Monitor specific accounts every 30 minutes
        const accountJob = cron.schedule('*/30 * * * *', async () => {
            await this.scrapeMonitoredAccounts();
        }, {
            scheduled: false
        });

        this.jobs.set('account-monitoring', accountJob);
        accountJob.start();

        this.logger.info('Account monitoring scheduled');
    }

    scheduleTrendingAnalysis() {
        // Check trending topics every hour
        const trendingJob = cron.schedule('0 * * * *', async () => {
            await this.scrapeTrendingContent();
        }, {
            scheduled: false
        });

        this.jobs.set('trending-analysis', trendingJob);
        trendingJob.start();

        this.logger.info('Trending analysis scheduled');
    }

    scheduleHealthChecks() {
        // Health check every 5 minutes
        const healthJob = cron.schedule('*/5 * * * *', async () => {
            await this.performHealthCheck();
        }, {
            scheduled: false
        });

        this.jobs.set('health-check', healthJob);
        healthJob.start();

        this.logger.info('Health checks scheduled');
    }

    async scrapeKeywords(keywords, priority = 'regular') {
        this.logger.info(`Starting ${priority} keyword scraping for ${keywords.length} keywords`);

        for (const keyword of keywords) {
            const jobId = await this.createScrapingJob('keyword', keyword, priority);
            
            for (const [platform, scraper] of Object.entries(this.scrapers)) {
                if (!scraper.isAuthenticated) {
                    this.logger.warn(`${platform} scraper not authenticated, skipping`);
                    continue;
                }

                try {
                    await this.updateJobStatus(jobId, 'running');
                    
                    const posts = await scraper.searchByKeyword(keyword, {
                        maxResults: priority === 'high-priority' ? 50 : 25
                    });

                    await this.updateJobResults(jobId, posts.length);
                    this.logger.info(`Scraped ${posts.length} posts for keyword "${keyword}" from ${platform}`);

                } catch (error) {
                    this.logger.error(`Error scraping keyword "${keyword}" from ${platform}:`, error);
                    await this.updateJobStatus(jobId, 'failed', error.message);
                }
            }

            await this.updateJobStatus(jobId, 'completed');
        }
    }

    async scrapeMonitoredAccounts() {
        this.logger.info('Starting monitored account scraping');

        for (const [platform, accounts] of Object.entries(this.monitoredAccounts)) {
            const scraper = this.scrapers[platform];
            
            if (!scraper || !scraper.isAuthenticated) {
                this.logger.warn(`${platform} scraper not available, skipping account monitoring`);
                continue;
            }

            for (const account of accounts) {
                const jobId = await this.createScrapingJob('user', account, 'account-monitoring');
                
                try {
                    await this.updateJobStatus(jobId, 'running');
                    
                    const posts = await scraper.getUserPosts(account, {
                        maxResults: 20
                    });

                    await this.updateJobResults(jobId, posts.length);
                    this.logger.info(`Scraped ${posts.length} posts from ${platform} account: ${account}`);
                    await this.updateJobStatus(jobId, 'completed');

                } catch (error) {
                    this.logger.error(`Error scraping ${platform} account ${account}:`, error);
                    await this.updateJobStatus(jobId, 'failed', error.message);
                }
            }
        }
    }

    async scrapeTrendingContent() {
        this.logger.info('Starting trending content scraping');

        for (const [platform, scraper] of Object.entries(this.scrapers)) {
            if (!scraper.isAuthenticated) {
                this.logger.warn(`${platform} scraper not authenticated, skipping trending`);
                continue;
            }

            const jobId = await this.createScrapingJob('trending', platform, 'trending');
            
            try {
                await this.updateJobStatus(jobId, 'running');
                
                const trending = await scraper.getTrendingContent();
                
                await this.updateJobResults(jobId, trending.length);
                this.logger.info(`Scraped ${trending.length} trending items from ${platform}`);
                await this.updateJobStatus(jobId, 'completed');

            } catch (error) {
                this.logger.error(`Error scraping trending content from ${platform}:`, error);
                await this.updateJobStatus(jobId, 'failed', error.message);
            }
        }
    }

    async performHealthCheck() {
        try {
            // Check scraper health
            for (const [platform, scraper] of Object.entries(this.scrapers)) {
                const stats = scraper.getStats();
                
                if (stats.errors > stats.postsScraped * 0.5) {
                    this.logger.warn(`High error rate detected for ${platform} scraper`, stats);
                }
            }

            // Check database connectivity
            await DatabaseManager.query('SELECT 1');

            // Check for stuck jobs
            const stuckJobs = await DatabaseManager.query(`
                SELECT * FROM scraping_jobs 
                WHERE status = 'running' 
                AND started_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
            `);

            if (stuckJobs.length > 0) {
                this.logger.warn(`Found ${stuckJobs.length} stuck jobs`);
                
                // Mark stuck jobs as failed
                for (const job of stuckJobs) {
                    await this.updateJobStatus(job.id, 'failed', 'Job timeout');
                }
            }

        } catch (error) {
            this.logger.error('Health check failed:', error);
        }
    }

    async createScrapingJob(jobType, searchQuery, priority = 'regular') {
        try {
            const result = await DatabaseManager.query(`
                INSERT INTO scraping_jobs (platform, job_type, search_query, config, started_at)
                VALUES ('multi', ?, ?, ?, NOW())
            `, [jobType, searchQuery, JSON.stringify({ priority })]);

            return result.insertId;
        } catch (error) {
            this.logger.error('Error creating scraping job:', error);
            return null;
        }
    }

    async updateJobStatus(jobId, status, errorMessage = null) {
        if (!jobId) return;

        try {
            const updateData = [status];
            let sql = 'UPDATE scraping_jobs SET status = ?';

            if (status === 'completed') {
                sql += ', completed_at = NOW()';
            }

            if (errorMessage) {
                sql += ', error_message = ?';
                updateData.push(errorMessage);
            }

            sql += ' WHERE id = ?';
            updateData.push(jobId);

            await DatabaseManager.query(sql, updateData);
        } catch (error) {
            this.logger.error('Error updating job status:', error);
        }
    }

    async updateJobResults(jobId, postsCollected) {
        if (!jobId) return;

        try {
            await DatabaseManager.query(`
                UPDATE scraping_jobs 
                SET posts_collected = posts_collected + ?
                WHERE id = ?
            `, [postsCollected, jobId]);
        } catch (error) {
            this.logger.error('Error updating job results:', error);
        }
    }

    // Manual job scheduling methods
    async scheduleCustomJob(platform, jobType, searchQuery, scheduleTime) {
        try {
            const job = schedule.scheduleJob(scheduleTime, async () => {
                const scraper = this.scrapers[platform];
                if (!scraper || !scraper.isAuthenticated) {
                    this.logger.error(`Cannot execute custom job: ${platform} scraper not available`);
                    return;
                }

                const jobId = await this.createScrapingJob(jobType, searchQuery, 'custom');
                
                try {
                    await this.updateJobStatus(jobId, 'running');
                    
                    let posts = [];
                    switch (jobType) {
                        case 'keyword':
                            posts = await scraper.searchByKeyword(searchQuery);
                            break;
                        case 'hashtag':
                            posts = await scraper.searchByHashtag(searchQuery);
                            break;
                        case 'user':
                            posts = await scraper.getUserPosts(searchQuery);
                            break;
                    }

                    await this.updateJobResults(jobId, posts.length);
                    await this.updateJobStatus(jobId, 'completed');
                    
                } catch (error) {
                    await this.updateJobStatus(jobId, 'failed', error.message);
                }
            });

            const jobKey = `custom-${Date.now()}`;
            this.jobs.set(jobKey, job);
            
            this.logger.info(`Scheduled custom job: ${jobKey}`);
            return jobKey;

        } catch (error) {
            this.logger.error('Error scheduling custom job:', error);
            throw error;
        }
    }

    getJobStatus() {
        const activeJobs = [];
        
        for (const [name, job] of this.jobs.entries()) {
            activeJobs.push({
                name,
                nextInvocation: job.nextInvocation(),
                running: job.running || false
            });
        }

        return {
            totalJobs: this.jobs.size,
            activeJobs,
            scraperStatus: Object.fromEntries(
                Object.entries(this.scrapers).map(([platform, scraper]) => [
                    platform,
                    {
                        authenticated: scraper.isAuthenticated,
                        stats: scraper.getStats()
                    }
                ])
            )
        };
    }

    stopAllJobs() {
        for (const [name, job] of this.jobs.entries()) {
            job.destroy();
            this.logger.info(`Stopped job: ${name}`);
        }
        this.jobs.clear();
    }

    async cleanup() {
        this.logger.info('Cleaning up scraping scheduler');
        this.stopAllJobs();
        
        for (const scraper of Object.values(this.scrapers)) {
            await scraper.cleanup();
        }
    }
}

module.exports = ScrapingScheduler;
