const request = require('supertest');
const SocialMediaScraperApp = require('../src/app');

describe('Social Media Scraper API', () => {
    let app;
    let server;

    beforeAll(async () => {
        // Create app instance but don't initialize services for testing
        app = new SocialMediaScraperApp();
        server = app.app;
    });

    afterAll(async () => {
        if (app && app.shutdown) {
            await app.shutdown();
        }
    });

    describe('Health Endpoints', () => {
        test('GET / should return API information', async () => {
            const response = await request(server)
                .get('/')
                .expect(200);

            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('version');
            expect(response.body).toHaveProperty('status', 'running');
            expect(response.body).toHaveProperty('endpoints');
        });

        test('GET /api/health should return health status', async () => {
            const response = await request(server)
                .get('/api/health')
                .expect(200);

            expect(response.body).toHaveProperty('status');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('uptime');
            expect(response.body).toHaveProperty('memory');
        });

        test('GET /api/scraping/platforms should return available platforms', async () => {
            const response = await request(server)
                .get('/api/scraping/platforms')
                .expect(200);

            expect(response.body).toHaveProperty('platforms');
            expect(response.body).toHaveProperty('total');
            expect(Array.isArray(response.body.platforms)).toBe(true);
        });
    });

    describe('Error Handling', () => {
        test('GET /nonexistent should return 404', async () => {
            const response = await request(server)
                .get('/nonexistent')
                .expect(404);

            expect(response.body).toHaveProperty('error', 'Endpoint not found');
        });

        test('POST /api/scraping/invalid/search/keyword should return 400', async () => {
            const response = await request(server)
                .post('/api/scraping/invalid/search/keyword')
                .send({ keyword: 'test' })
                .expect(400);

            expect(response.body).toHaveProperty('error', 'Invalid platform');
        });

        test('POST /api/scraping/twitter/search/keyword without keyword should return 400', async () => {
            const response = await request(server)
                .post('/api/scraping/twitter/search/keyword')
                .send({})
                .expect(400);

            expect(response.body).toHaveProperty('error', 'Missing keyword');
        });
    });

    describe('Content Processing', () => {
        const ContentProcessor = require('../src/scrapers/processors/ContentProcessor');
        let processor;

        beforeEach(() => {
            processor = new ContentProcessor();
        });

        test('should clean text properly', () => {
            const dirtyText = 'Hello &amp; welcome to   #MalawiElections @user https://example.com';
            const result = processor.cleanText(dirtyText);
            
            expect(result).not.toContain('&amp;');
            expect(result).not.toContain('https://example.com');
            expect(result).not.toContain('  '); // Multiple spaces
        });

        test('should extract hashtags', () => {
            const text = 'This is about #MalawiElections and #Democracy #Peace';
            const result = processor.extractHashtags(text);
            
            expect(result).toContain('#malawiElections'.toLowerCase());
            expect(result).toContain('#democracy'.toLowerCase());
            expect(result).toContain('#peace'.toLowerCase());
            expect(result).toHaveLength(3);
        });

        test('should extract mentions', () => {
            const text = 'Hello @user1 and @user2, what do you think?';
            const result = processor.extractMentions(text);
            
            expect(result).toContain('@user1');
            expect(result).toContain('@user2');
            expect(result).toHaveLength(2);
        });

        test('should detect language', () => {
            const englishText = 'This is an English text about elections';
            const chichewaText = 'Izi ndi za chisankho mu Malawi';
            
            expect(processor.detectLanguage(englishText)).toBe('en');
            // Note: Simple detection might not work perfectly for short texts
        });

        test('should analyze sentiment', async () => {
            const positiveText = 'I love peaceful elections and democracy';
            const negativeText = 'I hate violence and corruption';
            const neutralText = 'The election is scheduled for next month';
            
            const positiveSentiment = await processor.analyzeSentiment(positiveText);
            const negativeSentiment = await processor.analyzeSentiment(negativeText);
            const neutralSentiment = await processor.analyzeSentiment(neutralText);
            
            expect(positiveSentiment).toBeGreaterThan(0);
            expect(negativeSentiment).toBeLessThan(0);
            expect(Math.abs(neutralSentiment)).toBeLessThan(0.5);
        });

        test('should detect threats', async () => {
            const highThreatText = 'We will kill all opposition members';
            const mediumThreatText = 'There will be violence if we lose';
            const lowThreatText = 'We will protest peacefully';
            const safeThreatText = 'We support peaceful elections';
            
            const highThreat = await processor.detectThreats(highThreatText);
            const mediumThreat = await processor.detectThreats(mediumThreatText);
            const lowThreat = await processor.detectThreats(lowThreatText);
            const safeThreat = await processor.detectThreats(safeThreatText);
            
            expect(['high', 'critical']).toContain(highThreat);
            expect(['medium', 'high']).toContain(mediumThreat);
            expect(['low', 'medium']).toContain(lowThreat);
            expect(safeThreat).toBe('low');
        });
    });

    describe('Rate Limiting', () => {
        const RateLimiter = require('../src/scrapers/base/RateLimiter');
        let rateLimiter;

        beforeEach(() => {
            rateLimiter = new RateLimiter('twitter');
        });

        test('should extract endpoint from URL', () => {
            expect(rateLimiter.getEndpointFromUrl('https://api.twitter.com/2/tweets/search/recent')).toBe('search');
            expect(rateLimiter.getEndpointFromUrl('https://api.twitter.com/2/users/by/username/test')).toBe('user_timeline');
            expect(rateLimiter.getEndpointFromUrl('https://api.twitter.com/1.1/trends/place.json')).toBe('trending');
        });

        test('should get correct limits for endpoints', () => {
            const searchLimit = rateLimiter.getLimitForEndpoint('search');
            const userLimit = rateLimiter.getLimitForEndpoint('user_timeline');
            
            expect(searchLimit).toHaveProperty('requests');
            expect(searchLimit).toHaveProperty('window');
            expect(searchLimit.requests).toBeGreaterThan(0);
            expect(searchLimit.window).toBeGreaterThan(0);
        });
    });

    describe('User Agent Management', () => {
        const UserAgentManager = require('../src/scrapers/base/UserAgentManager');
        let userAgentManager;

        beforeEach(() => {
            userAgentManager = new UserAgentManager();
        });

        test('should return different user agents', () => {
            const ua1 = userAgentManager.getRandomUserAgent();
            const ua2 = userAgentManager.getRandomUserAgent();
            
            expect(typeof ua1).toBe('string');
            expect(typeof ua2).toBe('string');
            expect(ua1.length).toBeGreaterThan(50); // User agents are typically long
        });

        test('should return platform-specific user agents', () => {
            const twitterUA = userAgentManager.getRandomUserAgent('twitter');
            const facebookUA = userAgentManager.getRandomUserAgent('facebook');
            
            expect(typeof twitterUA).toBe('string');
            expect(typeof facebookUA).toBe('string');
        });

        test('should generate custom user agents', () => {
            const customUA = userAgentManager.generateCustomUserAgent({
                browser: 'chrome',
                os: 'windows',
                version: 'latest'
            });
            
            expect(typeof customUA).toBe('string');
            expect(customUA).toContain('Chrome');
            expect(customUA).toContain('Windows');
        });
    });
});

// Integration tests (require actual services)
describe('Integration Tests', () => {
    // These tests require actual database and Redis connections
    // Skip them if services are not available
    
    const DatabaseManager = require('../src/database/DatabaseManager');
    const CacheManager = require('../src/scrapers/storage/CacheManager');

    describe('Database Integration', () => {
        test('should connect to database', async () => {
            try {
                await DatabaseManager.initialize();
                const result = await DatabaseManager.query('SELECT 1 as test');
                expect(result[0].test).toBe(1);
                await DatabaseManager.close();
            } catch (error) {
                console.warn('Database not available for testing:', error.message);
                // Skip test if database not available
            }
        });
    });

    describe('Cache Integration', () => {
        test('should connect to Redis', async () => {
            try {
                await CacheManager.initialize();
                await CacheManager.set('test_key', 'test_value', 10);
                const value = await CacheManager.get('test_key');
                expect(value).toBe('test_value');
                await CacheManager.del('test_key');
                await CacheManager.close();
            } catch (error) {
                console.warn('Redis not available for testing:', error.message);
                // Skip test if Redis not available
            }
        });
    });
});
