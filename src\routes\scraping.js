const express = require('express');
const router = express.Router();
const { apiLogger } = require('../utils/logger');
const TwitterScraper = require('../scrapers/platforms/TwitterScraper');
const FacebookScraper = require('../scrapers/platforms/FacebookScraper');
const TikTokScraper = require('../scrapers/platforms/TikTokScraper');
const DatabaseManager = require('../database/DatabaseManager');

// Initialize scrapers
const scrapers = {
    twitter: new TwitterScraper(),
    facebook: new FacebookScraper(),
    tiktok: new TikTokScraper()
};

// Middleware to validate platform
const validatePlatform = (req, res, next) => {
    const { platform } = req.params;
    if (!scrapers[platform]) {
        return res.status(400).json({
            error: 'Invalid platform',
            message: `Platform '${platform}' is not supported. Available platforms: ${Object.keys(scrapers).join(', ')}`
        });
    }
    req.scraper = scrapers[platform];
    next();
};

// GET /api/scraping/platforms - List available platforms
router.get('/platforms', (req, res) => {
    const platforms = Object.keys(scrapers).map(platform => ({
        name: platform,
        status: scrapers[platform].isAuthenticated ? 'authenticated' : 'not_authenticated',
        stats: scrapers[platform].getStats()
    }));

    res.json({
        platforms,
        total: platforms.length
    });
});

// POST /api/scraping/:platform/search/keyword - Search by keyword
router.post('/:platform/search/keyword', validatePlatform, async (req, res) => {
    try {
        const { keyword, maxResults = 100, sinceId, untilId, startTime, endTime } = req.body;
        
        if (!keyword) {
            return res.status(400).json({
                error: 'Missing keyword',
                message: 'Keyword is required for search'
            });
        }

        apiLogger.info(`Searching ${req.params.platform} for keyword: ${keyword}`);

        const options = {
            maxResults: Math.min(maxResults, 500), // Limit to prevent abuse
            sinceId,
            untilId,
            startTime,
            endTime
        };

        const posts = await req.scraper.searchByKeyword(keyword, options);

        res.json({
            platform: req.params.platform,
            keyword,
            posts,
            count: posts.length,
            options
        });

    } catch (error) {
        apiLogger.error(`Error searching ${req.params.platform} for keyword:`, error);
        res.status(500).json({
            error: 'Search failed',
            message: error.message
        });
    }
});

// POST /api/scraping/:platform/search/hashtag - Search by hashtag
router.post('/:platform/search/hashtag', validatePlatform, async (req, res) => {
    try {
        const { hashtag, maxResults = 100, sinceId, untilId, startTime, endTime } = req.body;
        
        if (!hashtag) {
            return res.status(400).json({
                error: 'Missing hashtag',
                message: 'Hashtag is required for search'
            });
        }

        apiLogger.info(`Searching ${req.params.platform} for hashtag: ${hashtag}`);

        const options = {
            maxResults: Math.min(maxResults, 500),
            sinceId,
            untilId,
            startTime,
            endTime
        };

        const posts = await req.scraper.searchByHashtag(hashtag, options);

        res.json({
            platform: req.params.platform,
            hashtag,
            posts,
            count: posts.length,
            options
        });

    } catch (error) {
        apiLogger.error(`Error searching ${req.params.platform} for hashtag:`, error);
        res.status(500).json({
            error: 'Search failed',
            message: error.message
        });
    }
});

// POST /api/scraping/:platform/user/:username - Get user posts
router.post('/:platform/user/:username', validatePlatform, async (req, res) => {
    try {
        const { username } = req.params;
        const { maxResults = 100, sinceId, untilId, startTime, endTime } = req.body;

        apiLogger.info(`Fetching posts from ${req.params.platform} user: ${username}`);

        const options = {
            maxResults: Math.min(maxResults, 500),
            sinceId,
            untilId,
            startTime,
            endTime
        };

        const posts = await req.scraper.getUserPosts(username, options);

        res.json({
            platform: req.params.platform,
            username,
            posts,
            count: posts.length,
            options
        });

    } catch (error) {
        apiLogger.error(`Error fetching posts from ${req.params.platform} user ${req.params.username}:`, error);
        res.status(500).json({
            error: 'User posts fetch failed',
            message: error.message
        });
    }
});

// GET /api/scraping/:platform/post/:postId - Get specific post details
router.get('/:platform/post/:postId', validatePlatform, async (req, res) => {
    try {
        const { postId } = req.params;

        apiLogger.info(`Fetching ${req.params.platform} post details: ${postId}`);

        const post = await req.scraper.getPostDetails(postId);

        res.json({
            platform: req.params.platform,
            postId,
            post
        });

    } catch (error) {
        apiLogger.error(`Error fetching ${req.params.platform} post ${req.params.postId}:`, error);
        res.status(500).json({
            error: 'Post fetch failed',
            message: error.message
        });
    }
});

// GET /api/scraping/:platform/trending - Get trending content
router.get('/:platform/trending', validatePlatform, async (req, res) => {
    try {
        apiLogger.info(`Fetching trending content from ${req.params.platform}`);

        const trending = await req.scraper.getTrendingContent();

        res.json({
            platform: req.params.platform,
            trending,
            count: trending.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error(`Error fetching trending content from ${req.params.platform}:`, error);
        res.status(500).json({
            error: 'Trending fetch failed',
            message: error.message
        });
    }
});

// GET /api/scraping/:platform/stats - Get scraper statistics
router.get('/:platform/stats', validatePlatform, (req, res) => {
    try {
        const stats = req.scraper.getStats();
        
        res.json({
            platform: req.params.platform,
            stats,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error(`Error getting ${req.params.platform} stats:`, error);
        res.status(500).json({
            error: 'Stats fetch failed',
            message: error.message
        });
    }
});

// POST /api/scraping/batch - Batch scraping across multiple platforms
router.post('/batch', async (req, res) => {
    try {
        const { platforms, searchType, query, options = {} } = req.body;

        if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
            return res.status(400).json({
                error: 'Invalid platforms',
                message: 'Platforms array is required'
            });
        }

        if (!searchType || !query) {
            return res.status(400).json({
                error: 'Missing parameters',
                message: 'searchType and query are required'
            });
        }

        apiLogger.info(`Batch scraping: ${searchType} for "${query}" across platforms: ${platforms.join(', ')}`);

        const results = {};
        const errors = {};

        // Execute scraping for each platform in parallel
        const promises = platforms.map(async (platform) => {
            if (!scrapers[platform]) {
                errors[platform] = `Platform '${platform}' is not supported`;
                return;
            }

            try {
                let posts;
                switch (searchType) {
                    case 'keyword':
                        posts = await scrapers[platform].searchByKeyword(query, options);
                        break;
                    case 'hashtag':
                        posts = await scrapers[platform].searchByHashtag(query, options);
                        break;
                    case 'user':
                        posts = await scrapers[platform].getUserPosts(query, options);
                        break;
                    default:
                        throw new Error(`Invalid search type: ${searchType}`);
                }
                
                results[platform] = {
                    posts,
                    count: posts.length
                };
            } catch (error) {
                errors[platform] = error.message;
            }
        });

        await Promise.allSettled(promises);

        res.json({
            searchType,
            query,
            results,
            errors,
            summary: {
                totalPlatforms: platforms.length,
                successfulPlatforms: Object.keys(results).length,
                failedPlatforms: Object.keys(errors).length,
                totalPosts: Object.values(results).reduce((sum, result) => sum + result.count, 0)
            }
        });

    } catch (error) {
        apiLogger.error('Error in batch scraping:', error);
        res.status(500).json({
            error: 'Batch scraping failed',
            message: error.message
        });
    }
});

// GET /api/scraping/jobs - Get scraping jobs status
router.get('/jobs', async (req, res) => {
    try {
        const { status, platform, limit = 50, offset = 0 } = req.query;

        let sql = 'SELECT * FROM scraping_jobs WHERE 1=1';
        const params = [];

        if (status) {
            sql += ' AND status = ?';
            params.push(status);
        }

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const jobs = await DatabaseManager.query(sql, params);

        res.json({
            jobs,
            count: jobs.length,
            filters: { status, platform },
            pagination: { limit: parseInt(limit), offset: parseInt(offset) }
        });

    } catch (error) {
        apiLogger.error('Error fetching scraping jobs:', error);
        res.status(500).json({
            error: 'Jobs fetch failed',
            message: error.message
        });
    }
});

module.exports = router;
