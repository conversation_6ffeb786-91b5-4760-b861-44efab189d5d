{"name": "facebook-nodejs-business-sdk", "version": "18.0.4", "description": "SDK for the Facebook Marketing API in Javascript and Node.js", "author": "Facebook", "maintainers": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "homepage": "https://github.com/facebook/facebook-nodejs-business-sdk", "bugs": {"url": "https://github.com/facebook/facebook-nodejs-business-sdk/issues"}, "keywords": ["facebook", "meta", "ads", "sdk", "api", "javascript", "es6", "es2015", "isomorphic", "nodejs", "amd", "requirejs", "umd", "promises"], "repository": {"type": "git", "url": "git://github.com/facebook/facebook-nodejs-business-sdk.git"}, "dependencies": {"axios": "^1.4.0", "currency-codes": "^1.5.1", "iso-3166-1": "^2.1.1", "js-sha256": "^0.9.0", "mixwith": "~0.1.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.26.3", "babel-eslint": "^7.2.3", "babel-plugin-async-to-promises": "^1.0.5", "babel-plugin-external-helpers": "~6.8.0", "babel-plugin-transform-flow-strip-types": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-flow": "^6.23.0", "babel-preset-stage-2": "^6.24.1", "babelrc-rollup": "^3.0.0", "chai": "~3.5.0", "eslint": "^7.32.0", "eslint-plugin-flowtype": "^2.50.3", "flow-bin": "^0.138.0", "gulp": "^4.0.2", "gulp-cli": "^2.2.0", "gulp-filenames": "~4.0.0", "gulp-load-plugins": "^1.6.0", "gulp-open": "^3.0.1", "gulp-rename": "^1.2.3", "gulp-sourcemaps": "~2.1.1", "jshint-sourcemap-reporter": "0.0.1", "rollup-plugin-babel": "~2.6.1", "rollup-plugin-commonjs": "~5.0.5", "rollup-plugin-json": "~2.0.2", "rollup-plugin-node-resolve": "~2.0.0", "rollup-stream": "~1.14.0", "semistandard": "^11.0.0", "vinyl-buffer": "~1.0.0", "vinyl-source-stream": "~2.0.0"}, "scripts": {"prepublish": "gulp dist", "start": "semistandard --fix", "flow": "flow", "test": "jest"}, "main": "./dist/cjs.js", "license": "Platform License", "semistandard": {"globals": ["after", "after<PERSON>ach", "before", "beforeEach", "context", "define", "describe", "expect", "it", "requirejs"], "ignore": ["gulpfile.js", "/dist/", "/examples/"], "plugins": ["flowtype"], "parser": "babel-es<PERSON>"}}