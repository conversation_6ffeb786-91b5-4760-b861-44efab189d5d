# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=social_media_monitoring
DATABASE_USER=root
DATABASE_PASSWORD=
DATABASE_URL=mysql://root:your_password@localhost:3306/social_media_monitoring

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379

# Twitter API Configuration
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Facebook API Configuration
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token

# TikTok Configuration (Unofficial APIs)
TIKTOK_API_KEY=your_tiktok_api_key
TIKTOK_API_SECRET=your_tiktok_api_secret

# Proxy Configuration
PROXY_LIST=http://proxy1:8080,http://proxy2:8080
PROXY_AUTH=username:password
USE_PROXY=false

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
MAX_CONCURRENT_SCRAPERS=5
SCRAPING_INTERVAL_MINUTES=15

# Security Configuration
JWT_SECRET=your_jwt_secret_key
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret

# Monitoring & Alerts
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK=your_slack_webhook_url
ENABLE_ALERTS=true

# Rate Limiting
TWITTER_RATE_LIMIT=300
FACEBOOK_RATE_LIMIT=200
TIKTOK_RATE_LIMIT=100

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov

# Threat Detection
THREAT_KEYWORDS_FILE=./config/threat_keywords.json
POLITICAL_ENTITIES_FILE=./config/political_entities.json
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_THREAT_DETECTION=true

# Browser Configuration
HEADLESS_BROWSER=true
BROWSER_TIMEOUT=30000
USER_AGENT_ROTATION=true
