{"name": "social-media-scraper", "version": "1.0.0", "description": "Comprehensive social media scraping system for Facebook, Twitter/X, and TikTok", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "demo": "node start-local.js", "setup-local": "node setup-local.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "setup": "node scripts/setup.js"}, "keywords": ["social-media", "scraping", "twitter", "facebook", "tiktok", "sentiment-analysis", "threat-detection"], "author": "Social Media Monitoring Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "redis": "^4.6.0", "puppeteer": "^21.0.0", "playwright": "^1.40.0", "axios": "^1.5.0", "cheerio": "^1.0.0-rc.12", "dotenv": "^16.3.1", "moment": "^2.29.4", "cron": "^3.1.6", "winston": "^3.11.0", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "node-schedule": "^2.1.1", "proxy-agent": "^6.3.0", "user-agents": "^1.0.0", "socks-proxy-agent": "^8.0.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0", "natural": "^6.5.0", "sentiment": "^5.0.2", "compromise": "^14.10.0", "twitter-api-v2": "^1.15.0", "facebook-nodejs-business-sdk": "^18.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "eslint": "^8.50.0", "supertest": "^6.3.3", "@types/jest": "^29.5.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}