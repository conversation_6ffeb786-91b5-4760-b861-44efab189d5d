const { scraperLogger } = require('./logger');
const DatabaseManager = require('../database/DatabaseManager');
const CacheManager = require('../scrapers/storage/CacheManager');

class HealthMonitor {
    constructor() {
        this.logger = scraperLogger;
        this.isInitialized = false;
        this.healthCheckInterval = null;
        this.alertThresholds = {
            errorRate: 0.5, // 50% error rate
            responseTime: 30000, // 30 seconds
            memoryUsage: 0.9, // 90% memory usage
            diskUsage: 0.9, // 90% disk usage
            dbConnectionTimeout: 5000, // 5 seconds
            cacheConnectionTimeout: 3000 // 3 seconds
        };
        
        this.healthStatus = {
            overall: 'healthy',
            services: {
                database: { status: 'unknown', lastCheck: null },
                cache: { status: 'unknown', lastCheck: null },
                scrapers: { status: 'unknown', lastCheck: null },
                system: { status: 'unknown', lastCheck: null }
            },
            alerts: []
        };
    }

    async initialize() {
        try {
            this.logger.info('Initializing health monitor');
            
            // Perform initial health check
            await this.performFullHealthCheck();
            
            // Schedule regular health checks every 5 minutes
            this.healthCheckInterval = setInterval(async () => {
                await this.performFullHealthCheck();
            }, 5 * 60 * 1000);

            this.isInitialized = true;
            this.logger.info('Health monitor initialized successfully');

        } catch (error) {
            this.logger.error('Failed to initialize health monitor:', error);
            throw error;
        }
    }

    async performFullHealthCheck() {
        try {
            this.logger.debug('Performing full health check');

            // Check all services
            await Promise.allSettled([
                this.checkDatabaseHealth(),
                this.checkCacheHealth(),
                this.checkScrapersHealth(),
                this.checkSystemHealth()
            ]);

            // Update overall status
            this.updateOverallStatus();

            // Process alerts
            await this.processAlerts();

        } catch (error) {
            this.logger.error('Error during health check:', error);
        }
    }

    async checkDatabaseHealth() {
        const startTime = Date.now();
        
        try {
            // Test basic connectivity
            await DatabaseManager.query('SELECT 1 as test');
            
            // Test table access
            const postCount = await DatabaseManager.query('SELECT COUNT(*) as count FROM social_posts LIMIT 1');
            const jobCount = await DatabaseManager.query('SELECT COUNT(*) as count FROM scraping_jobs LIMIT 1');
            
            const responseTime = Date.now() - startTime;
            
            this.healthStatus.services.database = {
                status: 'healthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                details: {
                    postsCount: postCount[0].count,
                    jobsCount: jobCount[0].count
                }
            };

            if (responseTime > this.alertThresholds.dbConnectionTimeout) {
                this.addAlert('warning', 'database', `Database response time is high: ${responseTime}ms`);
            }

        } catch (error) {
            this.healthStatus.services.database = {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: error.message
            };
            
            this.addAlert('critical', 'database', `Database connection failed: ${error.message}`);
        }
    }

    async checkCacheHealth() {
        const startTime = Date.now();
        
        try {
            // Test write
            await CacheManager.set('health_test', 'test_value', 60);
            
            // Test read
            const value = await CacheManager.get('health_test');
            
            // Test delete
            await CacheManager.del('health_test');
            
            const responseTime = Date.now() - startTime;

            if (value !== 'test_value') {
                throw new Error('Cache read/write test failed');
            }

            this.healthStatus.services.cache = {
                status: 'healthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                details: {
                    operations: ['set', 'get', 'del']
                }
            };

            if (responseTime > this.alertThresholds.cacheConnectionTimeout) {
                this.addAlert('warning', 'cache', `Cache response time is high: ${responseTime}ms`);
            }

        } catch (error) {
            this.healthStatus.services.cache = {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: error.message
            };
            
            this.addAlert('critical', 'cache', `Cache connection failed: ${error.message}`);
        }
    }

    async checkScrapersHealth() {
        try {
            // Check recent scraping activity
            const recentActivity = await DatabaseManager.query(`
                SELECT 
                    platform, 
                    MAX(scraped_at) as last_activity, 
                    COUNT(*) as recent_posts,
                    AVG(CASE WHEN threat_level IN ('medium', 'high', 'critical') THEN 1 ELSE 0 END) as threat_rate
                FROM social_posts 
                WHERE scraped_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                GROUP BY platform
            `);

            // Check for failed jobs
            const failedJobs = await DatabaseManager.query(`
                SELECT platform, COUNT(*) as failed_count
                FROM scraping_jobs 
                WHERE status = 'failed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                GROUP BY platform
            `);

            const scraperStatus = {
                status: 'healthy',
                lastCheck: new Date().toISOString(),
                platforms: {},
                summary: {
                    totalRecentPosts: 0,
                    activePlatforms: 0,
                    failedJobs: 0
                }
            };

            // Process activity data
            for (const activity of recentActivity) {
                scraperStatus.platforms[activity.platform] = {
                    lastActivity: activity.last_activity,
                    recentPosts: activity.recent_posts,
                    threatRate: parseFloat(activity.threat_rate) || 0,
                    status: activity.recent_posts > 0 ? 'active' : 'idle'
                };
                
                scraperStatus.summary.totalRecentPosts += activity.recent_posts;
                scraperStatus.summary.activePlatforms++;
            }

            // Process failed jobs
            for (const failed of failedJobs) {
                if (scraperStatus.platforms[failed.platform]) {
                    scraperStatus.platforms[failed.platform].failedJobs = failed.failed_count;
                }
                scraperStatus.summary.failedJobs += failed.failed_count;
            }

            // Check for alerts
            if (scraperStatus.summary.totalRecentPosts === 0) {
                scraperStatus.status = 'warning';
                this.addAlert('warning', 'scrapers', 'No recent scraping activity detected');
            }

            if (scraperStatus.summary.failedJobs > 10) {
                scraperStatus.status = 'unhealthy';
                this.addAlert('critical', 'scrapers', `High number of failed jobs: ${scraperStatus.summary.failedJobs}`);
            }

            this.healthStatus.services.scrapers = scraperStatus;

        } catch (error) {
            this.healthStatus.services.scrapers = {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: error.message
            };
            
            this.addAlert('critical', 'scrapers', `Scraper health check failed: ${error.message}`);
        }
    }

    async checkSystemHealth() {
        try {
            const memoryUsage = process.memoryUsage();
            const cpuUsage = process.cpuUsage();
            const uptime = process.uptime();

            // Calculate memory usage percentage (rough estimate)
            const totalMemory = memoryUsage.heapTotal + memoryUsage.external;
            const usedMemory = memoryUsage.heapUsed;
            const memoryUsagePercent = usedMemory / totalMemory;

            const systemStatus = {
                status: 'healthy',
                lastCheck: new Date().toISOString(),
                metrics: {
                    memory: {
                        used: usedMemory,
                        total: totalMemory,
                        percentage: memoryUsagePercent,
                        rss: memoryUsage.rss,
                        external: memoryUsage.external
                    },
                    cpu: {
                        user: cpuUsage.user,
                        system: cpuUsage.system
                    },
                    uptime: uptime,
                    platform: process.platform,
                    nodeVersion: process.version
                }
            };

            // Check for memory alerts
            if (memoryUsagePercent > this.alertThresholds.memoryUsage) {
                systemStatus.status = 'warning';
                this.addAlert('warning', 'system', `High memory usage: ${(memoryUsagePercent * 100).toFixed(1)}%`);
            }

            // Check disk space (if possible)
            try {
                const fs = require('fs');
                const stats = fs.statSync('.');
                systemStatus.metrics.filesystem = {
                    accessible: true
                };
            } catch (fsError) {
                systemStatus.status = 'warning';
                this.addAlert('warning', 'system', 'Filesystem access issues detected');
            }

            this.healthStatus.services.system = systemStatus;

        } catch (error) {
            this.healthStatus.services.system = {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: error.message
            };
            
            this.addAlert('critical', 'system', `System health check failed: ${error.message}`);
        }
    }

    updateOverallStatus() {
        const serviceStatuses = Object.values(this.healthStatus.services).map(service => service.status);
        
        if (serviceStatuses.includes('unhealthy')) {
            this.healthStatus.overall = 'unhealthy';
        } else if (serviceStatuses.includes('warning')) {
            this.healthStatus.overall = 'degraded';
        } else if (serviceStatuses.every(status => status === 'healthy')) {
            this.healthStatus.overall = 'healthy';
        } else {
            this.healthStatus.overall = 'unknown';
        }
    }

    addAlert(severity, service, message) {
        const alert = {
            id: `${service}-${Date.now()}`,
            severity,
            service,
            message,
            timestamp: new Date().toISOString()
        };

        // Remove old alerts for the same service
        this.healthStatus.alerts = this.healthStatus.alerts.filter(
            existingAlert => existingAlert.service !== service || existingAlert.message !== message
        );

        // Add new alert
        this.healthStatus.alerts.push(alert);

        // Keep only last 50 alerts
        if (this.healthStatus.alerts.length > 50) {
            this.healthStatus.alerts = this.healthStatus.alerts.slice(-50);
        }

        this.logger.warn(`Health alert [${severity}] ${service}: ${message}`);
    }

    async processAlerts() {
        const criticalAlerts = this.healthStatus.alerts.filter(alert => alert.severity === 'critical');
        
        if (criticalAlerts.length > 0) {
            // Send notifications for critical alerts
            await this.sendCriticalAlerts(criticalAlerts);
        }
    }

    async sendCriticalAlerts(alerts) {
        try {
            // This would integrate with notification systems (email, Slack, etc.)
            this.logger.error(`CRITICAL ALERTS: ${alerts.length} critical issues detected`, {
                alerts: alerts.map(alert => ({
                    service: alert.service,
                    message: alert.message,
                    timestamp: alert.timestamp
                }))
            });

            // TODO: Implement actual notification sending
            // - Email notifications
            // - Slack webhooks
            // - SMS alerts
            // - Dashboard notifications

        } catch (error) {
            this.logger.error('Failed to send critical alerts:', error);
        }
    }

    getHealthStatus() {
        return {
            ...this.healthStatus,
            lastUpdated: new Date().toISOString()
        };
    }

    getHealthSummary() {
        return {
            overall: this.healthStatus.overall,
            services: Object.fromEntries(
                Object.entries(this.healthStatus.services).map(([name, service]) => [
                    name,
                    {
                        status: service.status,
                        lastCheck: service.lastCheck
                    }
                ])
            ),
            alertCount: this.healthStatus.alerts.length,
            criticalAlerts: this.healthStatus.alerts.filter(alert => alert.severity === 'critical').length
        };
    }

    async cleanup() {
        this.logger.info('Cleaning up health monitor');
        
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        
        this.isInitialized = false;
    }
}

module.exports = HealthMonitor;
