const axios = require('axios');
const BaseScraper = require('../base/BaseScraper');
const { scraperLogger } = require('../../utils/logger');

class FacebookScraper extends BaseScraper {
    constructor(config = {}) {
        super('facebook', config);
        
        this.appId = process.env.FACEBOOK_APP_ID;
        this.appSecret = process.env.FACEBOOK_APP_SECRET;
        this.accessToken = process.env.FACEBOOK_ACCESS_TOKEN;
        
        this.apiEndpoints = {
            pageSearch: 'https://graph.facebook.com/v18.0/search',
            pagePosts: 'https://graph.facebook.com/v18.0/{page-id}/posts',
            postDetails: 'https://graph.facebook.com/v18.0/{post-id}',
            pageInfo: 'https://graph.facebook.com/v18.0/{page-id}'
        };
    }

    async authenticate() {
        try {
            if (!this.appId || !this.appSecret) {
                throw new Error('Facebook API credentials not configured');
            }

            // If no access token provided, generate app access token
            if (!this.accessToken) {
                const response = await axios.get('https://graph.facebook.com/oauth/access_token', {
                    params: {
                        client_id: this.appId,
                        client_secret: this.appSecret,
                        grant_type: 'client_credentials'
                    }
                });
                
                this.accessToken = response.data.access_token;
            }

            // Test authentication by getting app info
            const testResponse = await axios.get('https://graph.facebook.com/v18.0/me', {
                params: {
                    access_token: this.accessToken
                }
            });

            this.logger.info(`Authenticated with Facebook API: ${testResponse.data.name || 'App'}`);
            
        } catch (error) {
            this.logger.error('Facebook authentication failed:', error);
            throw error;
        }
    }

    async searchByKeyword(keyword, options = {}) {
        const {
            maxResults = 100,
            pageTypes = ['page'],
            fields = 'id,name,about,category,fan_count'
        } = options;

        try {
            this.logger.info(`Searching Facebook for keyword: ${keyword}`);

            // First, search for pages related to the keyword
            const searchResponse = await this.makeRequest('https://graph.facebook.com/v18.0/search', {
                params: {
                    q: keyword,
                    type: 'page',
                    fields: fields,
                    limit: Math.min(maxResults, 100),
                    access_token: this.accessToken
                }
            });

            const pages = searchResponse.data || [];
            const allPosts = [];

            // Get posts from each relevant page
            for (const page of pages.slice(0, 10)) { // Limit to first 10 pages
                try {
                    const posts = await this.getPagePosts(page.id, { maxResults: 10 });
                    allPosts.push(...posts);
                } catch (error) {
                    this.logger.warn(`Failed to get posts from page ${page.id}:`, error.message);
                }
            }

            this.logger.info(`Found ${allPosts.length} posts for keyword: ${keyword}`);
            return allPosts;

        } catch (error) {
            this.logger.error(`Error searching Facebook for keyword ${keyword}:`, error);
            throw error;
        }
    }

    async searchByHashtag(hashtag, options = {}) {
        // Facebook doesn't have a direct hashtag search API
        // We'll search for the hashtag as a keyword
        const formattedHashtag = hashtag.startsWith('#') ? hashtag : `#${hashtag}`;
        return await this.searchByKeyword(formattedHashtag, options);
    }

    async getUserPosts(pageId, options = {}) {
        return await this.getPagePosts(pageId, options);
    }

    async getPagePosts(pageId, options = {}) {
        const {
            maxResults = 100,
            since = null,
            until = null,
            fields = 'id,message,created_time,likes.summary(true),comments.summary(true),shares'
        } = options;

        try {
            this.logger.info(`Fetching posts from Facebook page: ${pageId}`);

            const params = {
                fields: fields,
                limit: Math.min(maxResults, 100),
                access_token: this.accessToken
            };

            if (since) params.since = since;
            if (until) params.until = until;

            const response = await this.makeRequest(`https://graph.facebook.com/v18.0/${pageId}/posts`, {
                params
            });

            const posts = [];
            for (const post of response.data || []) {
                const processedPost = await this.processPost(post);
                await this.savePost(processedPost);
                posts.push(processedPost);
            }

            this.logger.info(`Found ${posts.length} posts from page: ${pageId}`);
            return posts;

        } catch (error) {
            this.logger.error(`Error fetching posts from Facebook page ${pageId}:`, error);
            throw error;
        }
    }

    async getPostDetails(postId) {
        try {
            this.logger.debug(`Fetching details for Facebook post: ${postId}`);

            const response = await this.makeRequest(`https://graph.facebook.com/v18.0/${postId}`, {
                params: {
                    fields: 'id,message,created_time,likes.summary(true),comments.summary(true),shares,from,attachments',
                    access_token: this.accessToken
                }
            });

            if (!response) {
                throw new Error(`Post ${postId} not found`);
            }

            const processedPost = await this.processPost(response);
            await this.savePost(processedPost);
            
            return processedPost;

        } catch (error) {
            this.logger.error(`Error fetching Facebook post details ${postId}:`, error);
            throw error;
        }
    }

    async getTrendingContent() {
        try {
            this.logger.info('Facebook does not provide trending content API');
            
            // Instead, we can get posts from popular pages
            const popularPages = [
                // Add popular Malawi Facebook pages here
                'MalawiGovernment',
                'MalawiNews24',
                // Add more as needed
            ];

            const trendingPosts = [];
            
            for (const pageId of popularPages) {
                try {
                    const posts = await this.getPagePosts(pageId, { maxResults: 5 });
                    trendingPosts.push(...posts);
                } catch (error) {
                    this.logger.warn(`Failed to get posts from trending page ${pageId}:`, error.message);
                }
            }

            // Sort by engagement (likes + comments + shares)
            trendingPosts.sort((a, b) => {
                const engagementA = (a.like_count || 0) + (a.comment_count || 0) + (a.share_count || 0);
                const engagementB = (b.like_count || 0) + (b.comment_count || 0) + (b.share_count || 0);
                return engagementB - engagementA;
            });

            this.logger.info(`Found ${trendingPosts.length} trending posts`);
            return trendingPosts.slice(0, 20); // Return top 20

        } catch (error) {
            this.logger.error('Error fetching Facebook trending content:', error);
            throw error;
        }
    }

    async standardizePost(rawPost) {
        try {
            return {
                post_id: rawPost.id,
                author_username: rawPost.from?.name || 'Unknown',
                author_id: rawPost.from?.id || null,
                content: rawPost.message || rawPost.story || '',
                like_count: rawPost.likes?.summary?.total_count || 0,
                comment_count: rawPost.comments?.summary?.total_count || 0,
                share_count: rawPost.shares?.count || 0,
                view_count: 0, // Facebook doesn't provide view count in basic API
                location: rawPost.place?.name || null,
                post_date: new Date(rawPost.created_time || Date.now()),
                language: 'en' // Facebook doesn't provide language detection in basic API
            };
        } catch (error) {
            this.logger.error('Error standardizing Facebook post:', error);
            return super.standardizePost(rawPost);
        }
    }

    async executeRequest(url, options) {
        try {
            const response = await axios({
                url,
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                params: {
                    access_token: this.accessToken,
                    ...options.params
                },
                data: options.data,
                timeout: options.timeout || this.config.timeout,
                proxy: options.proxy
            });

            // Check for Facebook API errors
            if (response.data.error) {
                throw new Error(`Facebook API Error: ${response.data.error.message}`);
            }

            return response.data;
        } catch (error) {
            if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded');
            }
            if (error.response?.data?.error) {
                throw new Error(`Facebook API Error: ${error.response.data.error.message}`);
            }
            throw error;
        }
    }

    // Web scraping fallback methods (for public pages only)
    async webScrapePublicPage(pageUrl, options = {}) {
        try {
            this.logger.info(`Web scraping Facebook public page: ${pageUrl}`);
            
            // This would require Puppeteer/Playwright implementation
            // For now, return empty array
            this.logger.warn('Web scraping not implemented yet');
            return [];
            
        } catch (error) {
            this.logger.error('Error in web scraping:', error);
            return [];
        }
    }

    async getPageInfo(pageId) {
        try {
            const response = await this.makeRequest(`https://graph.facebook.com/v18.0/${pageId}`, {
                params: {
                    fields: 'id,name,about,category,fan_count,website,phone,location',
                    access_token: this.accessToken
                }
            });

            return response;
        } catch (error) {
            this.logger.error(`Error getting page info for ${pageId}:`, error);
            return null;
        }
    }

    async getApiUsage() {
        try {
            // Facebook doesn't provide rate limit info in headers like Twitter
            // We'll return our internal rate limit status
            const rateLimitStatus = await this.rateLimiter.getAllRateLimitStatus();
            
            return {
                platform: 'facebook',
                rateLimits: rateLimitStatus,
                stats: this.getStats()
            };
        } catch (error) {
            this.logger.error('Error getting API usage:', error);
            return null;
        }
    }

    async cleanup() {
        await super.cleanup();
        // Facebook-specific cleanup
        this.accessToken = null;
    }
}

module.exports = FacebookScraper;
