# Social Media Scraper for Election Monitoring

A comprehensive Node.js application for monitoring social media platforms (Twitter/X, Facebook, TikTok) to detect threats, analyze sentiment, and track political discourse during elections in Malawi.

## Features

### Core Functionality
- **Multi-Platform Scraping**: Twitter/X, Facebook, and TikTok
- **Real-time Monitoring**: Automated keyword and hashtag tracking
- **Threat Detection**: AI-powered threat level assessment
- **Sentiment Analysis**: Emotional tone analysis of posts
- **Political Entity Recognition**: Identification of political figures and parties
- **Language Support**: English and Chichewa (Malawi local language)

### Technical Features
- **Rate Limiting**: Intelligent API rate limit management
- **Proxy Support**: IP rotation for anti-detection
- **Caching**: Redis-based caching for performance
- **Database**: MySQL for persistent data storage
- **Scheduling**: Automated scraping jobs with cron
- **Health Monitoring**: System health checks and alerts
- **RESTful API**: Complete API for data access and control

## Architecture

```
src/
├── app.js                          # Main application entry point
├── database/
│   └── DatabaseManager.js          # MySQL database management
├── scrapers/
│   ├── base/
│   │   ├── BaseScraper.js          # Abstract base scraper class
│   │   ├── RateLimiter.js          # Rate limiting logic
│   │   ├── ProxyManager.js         # Proxy rotation management
│   │   └── UserAgentManager.js     # User agent rotation
│   ├── platforms/
│   │   ├── TwitterScraper.js       # Twitter/X specific implementation
│   │   ├── FacebookScraper.js      # Facebook specific implementation
│   │   └── TikTokScraper.js        # TikTok specific implementation
│   ├── processors/
│   │   └── ContentProcessor.js     # Text processing and analysis
│   └── storage/
│       └── CacheManager.js         # Redis cache management
├── routes/
│   ├── scraping.js                 # Scraping API endpoints
│   ├── analytics.js                # Analytics API endpoints
│   └── health.js                   # Health monitoring endpoints
├── utils/
│   ├── logger.js                   # Logging configuration
│   ├── ScrapingScheduler.js        # Job scheduling
│   └── HealthMonitor.js            # System health monitoring
└── middleware/                     # Express middleware
```

## Installation

### Prerequisites
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Social Media API Keys (Twitter, Facebook)

### Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd social-media-scraper
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Set up database**
```bash
# Create MySQL database
mysql -u root -p -e "CREATE DATABASE social_media_monitoring;"

# Run migrations (automatic on first start)
npm run migrate
```

5. **Start Redis server**
```bash
redis-server
```

6. **Start the application**
```bash
# Development
npm run dev

# Production
npm start
```

## Configuration

### Environment Variables

#### Database Configuration
```env
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=social_media_monitoring
DATABASE_USER=root
DATABASE_PASSWORD=your_password
```

#### Social Media APIs
```env
# Twitter API v2
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Facebook Graph API
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token
```

#### Optional Configuration
```env
# Proxy settings
PROXY_LIST=http://proxy1:8080,http://proxy2:8080
USE_PROXY=false

# Rate limiting
TWITTER_RATE_LIMIT=300
FACEBOOK_RATE_LIMIT=200
TIKTOK_RATE_LIMIT=100

# Monitoring
ENABLE_THREAT_DETECTION=true
ENABLE_SENTIMENT_ANALYSIS=true
```

## API Documentation

### Scraping Endpoints

#### Search by Keyword
```http
POST /api/scraping/{platform}/search/keyword
Content-Type: application/json

{
  "keyword": "Malawi elections",
  "maxResults": 100,
  "startTime": "2024-01-01T00:00:00Z"
}
```

#### Search by Hashtag
```http
POST /api/scraping/{platform}/search/hashtag
Content-Type: application/json

{
  "hashtag": "#MalawiElections",
  "maxResults": 50
}
```

#### Get User Posts
```http
POST /api/scraping/{platform}/user/{username}
Content-Type: application/json

{
  "maxResults": 100
}
```

#### Batch Scraping
```http
POST /api/scraping/batch
Content-Type: application/json

{
  "platforms": ["twitter", "facebook"],
  "searchType": "keyword",
  "query": "Malawi elections",
  "options": {
    "maxResults": 50
  }
}
```

### Analytics Endpoints

#### Overview Analytics
```http
GET /api/analytics/overview?timeframe=24h&platforms=twitter,facebook
```

#### Sentiment Analysis
```http
GET /api/analytics/sentiment?timeframe=7d&platform=twitter&groupBy=day
```

#### Threat Detection
```http
GET /api/analytics/threats?timeframe=24h&threatLevel=high
```

#### Search Posts
```http
GET /api/analytics/search?query=violence&platform=twitter&sentiment=negative
```

### Health Monitoring

#### System Health
```http
GET /api/health
GET /api/health/detailed
GET /api/health/database
GET /api/health/cache
GET /api/health/scrapers
```

## Usage Examples

### Basic Monitoring Setup

1. **Monitor Election Keywords**
```javascript
// Search for election-related content
const response = await fetch('/api/scraping/batch', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    platforms: ['twitter', 'facebook'],
    searchType: 'keyword',
    query: 'Malawi elections',
    options: { maxResults: 100 }
  })
});
```

2. **Analyze Sentiment Trends**
```javascript
// Get sentiment analysis for the last 24 hours
const sentiment = await fetch('/api/analytics/sentiment?timeframe=24h&groupBy=hour');
```

3. **Monitor Threats**
```javascript
// Get high-threat posts
const threats = await fetch('/api/analytics/threats?threatLevel=high&timeframe=24h');
```

### Automated Monitoring

The system automatically monitors:
- **High-priority keywords** every 15 minutes
- **Regular keywords** every hour
- **Monitored accounts** every 30 minutes
- **Trending content** every hour

## Data Processing Pipeline

### 1. Content Collection
- API-based scraping with fallback to web scraping
- Rate limiting and proxy rotation
- Data standardization across platforms

### 2. Text Processing
- HTML entity removal and text cleaning
- Hashtag and mention extraction
- Language detection (English/Chichewa)

### 3. Analysis
- **Sentiment Analysis**: -1 (negative) to +1 (positive)
- **Threat Detection**: Low, Medium, High, Critical
- **Entity Recognition**: Political figures and organizations

### 4. Storage
- MySQL for persistent data
- Redis for caching and rate limiting
- JSON fields for flexible metadata storage

## Monitoring Keywords

### Default Keywords
- Malawi elections
- Political party names (MCP, DPP, UTM, etc.)
- Political leaders (Chakwera, Mutharika, Kabambe)
- Threat-related terms (violence, riot, attack)
- Location names (Lilongwe, Blantyre, Mzuzu)

### Threat Detection
The system categorizes threats based on keyword severity:
- **Critical**: assassination, kill, murder, bomb
- **High**: attack, violence, riot, uprising
- **Medium**: threat, intimidate, hostile
- **Low**: protest, demonstration, rally

## Legal and Ethical Considerations

### Compliance
- Respects platform Terms of Service
- Uses official APIs where possible
- Implements proper rate limiting
- Public data only (no private content)

### Privacy
- No personal data collection
- Anonymized user identifiers
- GDPR-compliant data handling
- Configurable data retention policies

### Responsible Use
- Election monitoring and threat detection only
- No commercial use of scraped data
- Transparent methodology
- Regular security audits

## Troubleshooting

### Common Issues

1. **API Rate Limits**
   - Check rate limit status: `GET /api/health/scrapers`
   - Adjust scraping frequency in scheduler
   - Verify API credentials

2. **Database Connection**
   - Check database health: `GET /api/health/database`
   - Verify MySQL service is running
   - Check connection credentials

3. **Redis Connection**
   - Check cache health: `GET /api/health/cache`
   - Verify Redis service is running
   - Check Redis configuration

### Logs
- Application logs: `logs/combined.log`
- Error logs: `logs/error.log`
- Scraping logs: `logs/scraping.log`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details

## Disclaimer

This tool is designed for legitimate election monitoring and research purposes. Users are responsible for ensuring compliance with applicable laws and platform terms of service.
