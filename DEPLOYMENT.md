# Deployment Guide

This guide covers deploying the Social Media Scraper for election monitoring in production environments.

## Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ or CentOS 8+
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: 50GB+ available disk space
- **Network**: Stable internet connection with sufficient bandwidth

### Required Services
- **Node.js**: Version 18.0.0 or higher
- **MySQL**: Version 8.0 or higher
- **Redis**: Version 6.0 or higher
- **Process Manager**: PM2 or systemd

## Installation Steps

### 1. System Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MySQL 8.0
sudo apt install mysql-server-8.0 -y
sudo mysql_secure_installation

# Install Redis
sudo apt install redis-server -y
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Install PM2 globally
sudo npm install -g pm2
```

### 2. Application Setup

```bash
# Clone or upload application files
cd /opt
sudo git clone <repository-url> social-media-scraper
cd social-media-scraper

# Install dependencies
sudo npm install --production

# Set up environment
sudo cp .env.example .env
sudo nano .env  # Configure with production values

# Create necessary directories
sudo mkdir -p logs data uploads
sudo chown -R www-data:www-data logs data uploads

# Run setup script
sudo node scripts/setup.js
```

### 3. Database Configuration

```bash
# Create database and user
sudo mysql -u root -p << EOF
CREATE DATABASE social_media_monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'scraper_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON social_media_monitoring.* TO 'scraper_user'@'localhost';
FLUSH PRIVILEGES;
EOF

# Update .env with database credentials
DATABASE_USER=scraper_user
DATABASE_PASSWORD=secure_password_here
```

### 4. Security Configuration

```bash
# Create dedicated user
sudo useradd -r -s /bin/false scraper

# Set file permissions
sudo chown -R scraper:scraper /opt/social-media-scraper
sudo chmod -R 755 /opt/social-media-scraper
sudo chmod 600 /opt/social-media-scraper/.env

# Configure firewall
sudo ufw allow 22    # SSH
sudo ufw allow 3000  # Application (if direct access needed)
sudo ufw enable
```

## Production Deployment Options

### Option 1: PM2 Process Manager

```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'social-media-scraper',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
};
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Option 2: Systemd Service

```bash
# Copy service file
sudo cp scripts/social-media-scraper.service /etc/systemd/system/

# Edit service file with correct paths
sudo nano /etc/systemd/system/social-media-scraper.service

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable social-media-scraper
sudo systemctl start social-media-scraper
```

### Option 3: Docker Deployment

```bash
# Build Docker image
docker build -t social-media-scraper .

# Run with Docker Compose
docker-compose up -d

# Or run standalone
docker run -d \
  --name social-media-scraper \
  -p 3000:3000 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  --env-file .env \
  social-media-scraper
```

## Reverse Proxy Setup (Nginx)

```nginx
# /etc/nginx/sites-available/social-media-scraper
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/social-media-scraper /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Optional: Set up SSL with Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Environment Configuration

### Production .env Template

```env
# Production Environment
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Database (Production)
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=social_media_monitoring
DATABASE_USER=scraper_user
DATABASE_PASSWORD=your_secure_password

# Redis (Production)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# API Keys (REQUIRED)
TWITTER_BEARER_TOKEN=your_production_twitter_token
FACEBOOK_ACCESS_TOKEN=your_production_facebook_token

# Security
JWT_SECRET=your_very_secure_jwt_secret_key
BCRYPT_ROUNDS=12

# Monitoring
ENABLE_ALERTS=true
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK=your_slack_webhook_url

# Performance
MAX_CONCURRENT_SCRAPERS=3
SCRAPING_INTERVAL_MINUTES=15
```

## Monitoring and Maintenance

### Log Management

```bash
# Set up log rotation
sudo nano /etc/logrotate.d/social-media-scraper

# Content:
/opt/social-media-scraper/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 scraper scraper
    postrotate
        systemctl reload social-media-scraper
    endscript
}
```

### Health Monitoring

```bash
# Create monitoring script
cat > /opt/social-media-scraper/scripts/health-check.sh << 'EOF'
#!/bin/bash
HEALTH_URL="http://localhost:3000/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Health check passed"
    exit 0
else
    echo "$(date): Health check failed (HTTP $RESPONSE)"
    # Restart service
    systemctl restart social-media-scraper
    exit 1
fi
EOF

chmod +x /opt/social-media-scraper/scripts/health-check.sh

# Add to crontab
echo "*/5 * * * * /opt/social-media-scraper/scripts/health-check.sh >> /var/log/health-check.log 2>&1" | sudo crontab -
```

### Database Backup

```bash
# Create backup script
cat > /opt/social-media-scraper/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/social-media-scraper"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="social_media_monitoring"

mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u scraper_user -p$DATABASE_PASSWORD $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 7 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "$(date): Backup completed - $BACKUP_DIR/db_backup_$DATE.sql.gz"
EOF

chmod +x /opt/social-media-scraper/scripts/backup.sh

# Schedule daily backups
echo "0 2 * * * /opt/social-media-scraper/scripts/backup.sh >> /var/log/backup.log 2>&1" | sudo crontab -
```

## Performance Optimization

### Database Optimization

```sql
-- Add indexes for better performance
USE social_media_monitoring;

-- Optimize for time-based queries
ALTER TABLE social_posts ADD INDEX idx_scraped_date (scraped_at, platform);
ALTER TABLE social_posts ADD INDEX idx_threat_date (threat_level, post_date);

-- Optimize for search queries
ALTER TABLE social_posts ADD FULLTEXT INDEX idx_content_search (content, author_username);
```

### Redis Configuration

```bash
# Edit Redis config
sudo nano /etc/redis/redis.conf

# Recommended settings:
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## Security Hardening

### Application Security

```bash
# Set up fail2ban for SSH protection
sudo apt install fail2ban
sudo systemctl enable fail2ban

# Configure application-specific security
sudo nano /opt/social-media-scraper/.env

# Add security headers and rate limiting
ENABLE_SECURITY_HEADERS=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
```

### Network Security

```bash
# Configure iptables rules
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -j DROP

# Save rules
sudo iptables-save > /etc/iptables/rules.v4
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check logs: `journalctl -u social-media-scraper -f`
   - Verify environment variables
   - Check database connectivity

2. **High memory usage**
   - Monitor with: `pm2 monit`
   - Adjust `MAX_CONCURRENT_SCRAPERS`
   - Check for memory leaks in logs

3. **API rate limits**
   - Check rate limit status: `curl localhost:3000/api/health/scrapers`
   - Adjust scraping intervals
   - Verify API credentials

### Emergency Procedures

```bash
# Quick restart
sudo systemctl restart social-media-scraper

# Emergency stop
sudo systemctl stop social-media-scraper

# Check system resources
htop
df -h
free -h

# View recent logs
tail -f /opt/social-media-scraper/logs/combined.log
```

## Scaling Considerations

For high-volume monitoring:

1. **Horizontal Scaling**: Deploy multiple instances with load balancer
2. **Database Sharding**: Partition data by platform or time
3. **Caching Layer**: Implement Redis Cluster
4. **Queue System**: Add job queue for scraping tasks
5. **Monitoring**: Implement comprehensive monitoring with Prometheus/Grafana

## Support and Maintenance

- Regular security updates
- API credential rotation
- Performance monitoring
- Backup verification
- Log analysis for threats

For additional support, refer to the main README.md and API documentation.
