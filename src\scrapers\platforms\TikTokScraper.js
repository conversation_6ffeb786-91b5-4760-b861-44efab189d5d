const axios = require('axios');
const BaseScraper = require('../base/BaseScraper');
const { scraperLogger } = require('../../utils/logger');

class TikTokScraper extends BaseScraper {
    constructor(config = {}) {
        super('tiktok', config);
        
        this.apiKey = process.env.TIKTOK_API_KEY;
        this.apiSecret = process.env.TIKTOK_API_SECRET;
        
        // Using unofficial TikTok API endpoints
        this.apiEndpoints = {
            userVideos: 'https://api.tiktokv.com/aweme/v1/aweme/post/',
            videoDetails: 'https://api.tiktokv.com/aweme/v1/aweme/detail/',
            hashtag: 'https://api.tiktokv.com/aweme/v1/challenge/aweme/',
            trending: 'https://api.tiktokv.com/aweme/v1/feed/',
            search: 'https://api.tiktokv.com/aweme/v1/general/search/'
        };

        // Alternative endpoints (these may change frequently)
        this.alternativeEndpoints = {
            userInfo: 'https://www.tiktok.com/api/user/detail/',
            videoInfo: 'https://www.tiktok.com/api/item/detail/',
            trending: 'https://www.tiktok.com/api/recommend/item_list/'
        };
    }

    async authenticate() {
        try {
            // TikTok doesn't have a traditional authentication for unofficial APIs
            // We'll test connectivity instead
            this.logger.info('TikTok scraper initialized (no authentication required for unofficial APIs)');
            
            // Test with a simple request
            try {
                await this.testConnectivity();
                this.logger.info('TikTok API connectivity test successful');
            } catch (error) {
                this.logger.warn('TikTok API connectivity test failed, will use web scraping fallback');
            }
            
        } catch (error) {
            this.logger.error('TikTok initialization failed:', error);
            throw error;
        }
    }

    async testConnectivity() {
        // Test with a simple request to check if endpoints are accessible
        const response = await axios.get('https://www.tiktok.com/', {
            timeout: 10000,
            headers: {
                'User-Agent': this.userAgentManager.getRandomUserAgent('tiktok')
            }
        });
        
        if (response.status !== 200) {
            throw new Error('TikTok connectivity test failed');
        }
    }

    async searchByKeyword(keyword, options = {}) {
        const {
            maxResults = 100,
            offset = 0,
            sortType = 0 // 0: comprehensive, 1: most liked, 2: newest
        } = options;

        try {
            this.logger.info(`Searching TikTok for keyword: ${keyword}`);

            // Try unofficial API first
            try {
                const posts = await this.searchViaAPI(keyword, options);
                if (posts.length > 0) {
                    return posts;
                }
            } catch (error) {
                this.logger.warn('API search failed, falling back to web scraping:', error.message);
            }

            // Fallback to web scraping
            return await this.webScrapeSearch(keyword, options);

        } catch (error) {
            this.logger.error(`Error searching TikTok for keyword ${keyword}:`, error);
            throw error;
        }
    }

    async searchViaAPI(keyword, options = {}) {
        const {
            maxResults = 100,
            offset = 0,
            sortType = 0
        } = options;

        const response = await this.makeRequest(this.apiEndpoints.search, {
            params: {
                keyword: keyword,
                count: Math.min(maxResults, 50),
                cursor: offset,
                sort_type: sortType,
                search_source: 'normal_search'
            }
        });

        const posts = [];
        const videos = response.aweme_list || [];

        for (const video of videos) {
            const processedPost = await this.processPost(video);
            await this.savePost(processedPost);
            posts.push(processedPost);
        }

        return posts;
    }

    async searchByHashtag(hashtag, options = {}) {
        const {
            maxResults = 100,
            cursor = 0
        } = options;

        try {
            const formattedHashtag = hashtag.startsWith('#') ? hashtag.slice(1) : hashtag;
            this.logger.info(`Searching TikTok for hashtag: #${formattedHashtag}`);

            // Try to get hashtag challenge ID first
            const challengeId = await this.getHashtagChallengeId(formattedHashtag);
            
            if (challengeId) {
                const response = await this.makeRequest(this.apiEndpoints.hashtag, {
                    params: {
                        ch_id: challengeId,
                        count: Math.min(maxResults, 50),
                        cursor: cursor
                    }
                });

                const posts = [];
                const videos = response.aweme_list || [];

                for (const video of videos) {
                    const processedPost = await this.processPost(video);
                    await this.savePost(processedPost);
                    posts.push(processedPost);
                }

                this.logger.info(`Found ${posts.length} posts for hashtag: #${formattedHashtag}`);
                return posts;
            } else {
                // Fallback to keyword search
                return await this.searchByKeyword(`#${formattedHashtag}`, options);
            }

        } catch (error) {
            this.logger.error(`Error searching TikTok for hashtag ${hashtag}:`, error);
            throw error;
        }
    }

    async getUserPosts(username, options = {}) {
        const {
            maxResults = 100,
            maxCursor = 0
        } = options;

        try {
            this.logger.info(`Fetching TikTok posts from user: ${username}`);

            // Get user ID first
            const userId = await this.getUserId(username);
            
            if (!userId) {
                throw new Error(`User ${username} not found`);
            }

            const response = await this.makeRequest(this.apiEndpoints.userVideos, {
                params: {
                    user_id: userId,
                    count: Math.min(maxResults, 50),
                    max_cursor: maxCursor
                }
            });

            const posts = [];
            const videos = response.aweme_list || [];

            for (const video of videos) {
                const processedPost = await this.processPost(video);
                await this.savePost(processedPost);
                posts.push(processedPost);
            }

            this.logger.info(`Found ${posts.length} posts from user: ${username}`);
            return posts;

        } catch (error) {
            this.logger.error(`Error fetching TikTok posts from user ${username}:`, error);
            throw error;
        }
    }

    async getPostDetails(videoId) {
        try {
            this.logger.debug(`Fetching details for TikTok video: ${videoId}`);

            const response = await this.makeRequest(this.apiEndpoints.videoDetails, {
                params: {
                    aweme_id: videoId
                }
            });

            if (!response.aweme_detail) {
                throw new Error(`Video ${videoId} not found`);
            }

            const processedPost = await this.processPost(response.aweme_detail);
            await this.savePost(processedPost);
            
            return processedPost;

        } catch (error) {
            this.logger.error(`Error fetching TikTok video details ${videoId}:`, error);
            throw error;
        }
    }

    async getTrendingContent() {
        try {
            this.logger.info('Fetching trending content from TikTok');

            const response = await this.makeRequest(this.apiEndpoints.trending, {
                params: {
                    count: 50,
                    type: 0, // 0: For You, 1: Following
                    max_cursor: 0
                }
            });

            const posts = [];
            const videos = response.aweme_list || [];

            for (const video of videos) {
                const processedPost = await this.processPost(video);
                await this.savePost(processedPost);
                posts.push(processedPost);
            }

            this.logger.info(`Found ${posts.length} trending videos`);
            return posts;

        } catch (error) {
            this.logger.error('Error fetching TikTok trending content:', error);
            throw error;
        }
    }

    async standardizePost(rawVideo) {
        try {
            const author = rawVideo.author || {};
            const statistics = rawVideo.statistics || {};
            
            return {
                post_id: rawVideo.aweme_id || rawVideo.id,
                author_username: author.unique_id || author.nickname || 'Unknown',
                author_id: author.uid || author.sec_uid,
                content: rawVideo.desc || '',
                like_count: statistics.digg_count || 0,
                comment_count: statistics.comment_count || 0,
                share_count: statistics.share_count || 0,
                view_count: statistics.play_count || 0,
                location: rawVideo.poi_info?.poi_name || null,
                post_date: new Date((rawVideo.create_time || Date.now() / 1000) * 1000),
                language: rawVideo.text_extra?.[0]?.lang || 'en'
            };
        } catch (error) {
            this.logger.error('Error standardizing TikTok post:', error);
            return super.standardizePost(rawVideo);
        }
    }

    async executeRequest(url, options) {
        try {
            const response = await axios({
                url,
                method: options.method || 'GET',
                headers: {
                    'User-Agent': this.userAgentManager.getRandomUserAgent('tiktok'),
                    'Accept': 'application/json',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Referer': 'https://www.tiktok.com/',
                    ...options.headers
                },
                params: options.params,
                data: options.data,
                timeout: options.timeout || this.config.timeout,
                proxy: options.proxy
            });

            return response.data;
        } catch (error) {
            if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded');
            }
            throw error;
        }
    }

    // Helper methods
    async getUserId(username) {
        try {
            // This would need to be implemented based on available APIs
            // For now, return the username as ID
            return username;
        } catch (error) {
            this.logger.error(`Error getting user ID for ${username}:`, error);
            return null;
        }
    }

    async getHashtagChallengeId(hashtag) {
        try {
            // This would need to be implemented based on available APIs
            // For now, return null to fallback to keyword search
            return null;
        } catch (error) {
            this.logger.error(`Error getting challenge ID for hashtag ${hashtag}:`, error);
            return null;
        }
    }

    // Web scraping fallback methods
    async webScrapeSearch(keyword, options = {}) {
        try {
            this.logger.info(`Web scraping TikTok search for: ${keyword}`);
            
            // This would require Puppeteer/Playwright implementation
            // TikTok heavily relies on JavaScript, so web scraping is complex
            this.logger.warn('Web scraping not implemented yet');
            return [];
            
        } catch (error) {
            this.logger.error('Error in web scraping:', error);
            return [];
        }
    }

    async getApiUsage() {
        try {
            const rateLimitStatus = await this.rateLimiter.getAllRateLimitStatus();
            
            return {
                platform: 'tiktok',
                rateLimits: rateLimitStatus,
                stats: this.getStats(),
                note: 'Using unofficial APIs - rate limits may vary'
            };
        } catch (error) {
            this.logger.error('Error getting API usage:', error);
            return null;
        }
    }

    async cleanup() {
        await super.cleanup();
        // TikTok-specific cleanup
    }
}

module.exports = TikTokScraper;
