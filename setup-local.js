#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🚀 Social Media Scraper - Local Setup\n');

async function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupLocal() {
    console.log('This script will help you set up the Social Media Scraper on your local machine.\n');

    // Check if .env exists
    const envPath = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envPath)) {
        console.log('❌ .env file not found. Please run: cp .env.example .env');
        process.exit(1);
    }

    console.log('✅ .env file found\n');

    // Create necessary directories
    const directories = ['logs', 'data', 'uploads'];
    console.log('📁 Creating directories...');
    directories.forEach(dir => {
        const dirPath = path.join(process.cwd(), dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ Created: ${dir}/`);
        } else {
            console.log(`✅ Exists: ${dir}/`);
        }
    });

    console.log('\n🔧 Configuration Setup\n');

    // Database setup
    console.log('📊 Database Configuration:');
    console.log('The system needs MySQL to store scraped data.');
    
    const hasMySQL = await question('Do you have MySQL installed and running? (y/n): ');
    
    if (hasMySQL.toLowerCase() !== 'y') {
        console.log('\n📋 MySQL Installation Instructions:');
        console.log('Windows: Download from https://dev.mysql.com/downloads/mysql/');
        console.log('macOS: brew install mysql');
        console.log('Linux: sudo apt install mysql-server');
        console.log('\nAfter installation, create a database:');
        console.log('mysql -u root -p');
        console.log('CREATE DATABASE social_media_monitoring;');
        console.log('\nThen run this setup script again.');
        process.exit(0);
    }

    const dbPassword = await question('Enter your MySQL root password (or press Enter if none): ');
    
    // Update .env with database password
    let envContent = fs.readFileSync(envPath, 'utf8');
    envContent = envContent.replace('DATABASE_PASSWORD=', `DATABASE_PASSWORD=${dbPassword}`);
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Database configuration updated');

    // Redis setup
    console.log('\n🔴 Redis Configuration:');
    console.log('Redis is used for caching and rate limiting.');
    
    const hasRedis = await question('Do you have Redis installed and running? (y/n): ');
    
    if (hasRedis.toLowerCase() !== 'y') {
        console.log('\n📋 Redis Installation Instructions:');
        console.log('Windows: Download from https://github.com/microsoftarchive/redis/releases');
        console.log('macOS: brew install redis');
        console.log('Linux: sudo apt install redis-server');
        console.log('\nStart Redis: redis-server');
        console.log('\nThen run this setup script again.');
        process.exit(0);
    }

    // API Keys setup
    console.log('\n🔑 API Keys Configuration:');
    console.log('The system can work with different social media APIs.');
    console.log('You can start with just one platform and add others later.\n');

    const setupTwitter = await question('Do you want to set up Twitter API? (y/n): ');
    if (setupTwitter.toLowerCase() === 'y') {
        console.log('\n📱 Twitter API Setup:');
        console.log('1. Go to https://developer.twitter.com/');
        console.log('2. Create a new app');
        console.log('3. Get your API keys from the "Keys and tokens" tab');
        
        const twitterBearer = await question('Enter Twitter Bearer Token: ');
        if (twitterBearer) {
            envContent = envContent.replace('TWITTER_BEARER_TOKEN=your_twitter_bearer_token', `TWITTER_BEARER_TOKEN=${twitterBearer}`);
        }
    }

    const setupFacebook = await question('\nDo you want to set up Facebook API? (y/n): ');
    if (setupFacebook.toLowerCase() === 'y') {
        console.log('\n📘 Facebook API Setup:');
        console.log('1. Go to https://developers.facebook.com/');
        console.log('2. Create a new app');
        console.log('3. Add the Graph API product');
        console.log('4. Get your App ID and App Secret');
        
        const fbAppId = await question('Enter Facebook App ID: ');
        const fbAppSecret = await question('Enter Facebook App Secret: ');
        
        if (fbAppId) {
            envContent = envContent.replace('FACEBOOK_APP_ID=your_facebook_app_id', `FACEBOOK_APP_ID=${fbAppId}`);
        }
        if (fbAppSecret) {
            envContent = envContent.replace('FACEBOOK_APP_SECRET=your_facebook_app_secret', `FACEBOOK_APP_SECRET=${fbAppSecret}`);
        }
    }

    // Save updated .env
    fs.writeFileSync(envPath, envContent);
    console.log('✅ API configuration updated');

    // Test mode setup
    console.log('\n🧪 Test Mode:');
    const testMode = await question('Start in test mode (no API calls, demo data only)? (y/n): ');
    
    if (testMode.toLowerCase() === 'y') {
        envContent = fs.readFileSync(envPath, 'utf8');
        envContent += '\n# Test Mode\nTEST_MODE=true\nUSE_DEMO_DATA=true\n';
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Test mode enabled');
    }

    console.log('\n🎉 Setup Complete!\n');
    console.log('Next steps:');
    console.log('1. Start your MySQL server');
    console.log('2. Start your Redis server');
    console.log('3. Run: npm run dev');
    console.log('4. Open: http://localhost:3000/api/health');
    console.log('\n📚 Documentation:');
    console.log('- README.md for detailed instructions');
    console.log('- API docs at http://localhost:3000/api/health');
    console.log('\n⚠️  Note: The system will create database tables automatically on first run.');

    rl.close();
}

setupLocal().catch(console.error);
