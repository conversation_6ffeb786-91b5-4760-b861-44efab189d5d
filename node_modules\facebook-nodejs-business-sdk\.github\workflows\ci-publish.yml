name: Publish

on:
  push:
    tags:
      - v*

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 12
      - name: Deploy
        run: |
          npm install
          npm config set '//registry.npmjs.org/:_authToken' "${NPM_TOKEN}"
          npm publish
        env:
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
