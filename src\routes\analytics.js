const express = require('express');
const router = express.Router();
const { apiLogger } = require('../utils/logger');
const DatabaseManager = require('../database/DatabaseManager');
const CacheManager = require('../scrapers/storage/CacheManager');

// GET /api/analytics/overview - Get overall analytics overview
router.get('/overview', async (req, res) => {
    try {
        const { timeframe = '24h', platforms } = req.query;
        
        // Calculate time range
        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);
        
        // Build platform filter
        let platformFilter = '';
        const params = [startTime];
        
        if (platforms) {
            const platformList = platforms.split(',');
            platformFilter = ` AND platform IN (${platformList.map(() => '?').join(',')})`;
            params.push(...platformList);
        }

        // Get total posts
        const totalPostsQuery = `
            SELECT COUNT(*) as total_posts, platform
            FROM social_posts 
            WHERE scraped_at >= ?${platformFilter}
            GROUP BY platform
        `;
        const totalPosts = await DatabaseManager.query(totalPostsQuery, params);

        // Get sentiment distribution
        const sentimentQuery = `
            SELECT 
                CASE 
                    WHEN sentiment_score > 0.1 THEN 'positive'
                    WHEN sentiment_score < -0.1 THEN 'negative'
                    ELSE 'neutral'
                END as sentiment,
                COUNT(*) as count,
                platform
            FROM social_posts 
            WHERE scraped_at >= ? AND sentiment_score IS NOT NULL${platformFilter}
            GROUP BY sentiment, platform
        `;
        const sentimentData = await DatabaseManager.query(sentimentQuery, params);

        // Get threat level distribution
        const threatQuery = `
            SELECT threat_level, COUNT(*) as count, platform
            FROM social_posts 
            WHERE scraped_at >= ?${platformFilter}
            GROUP BY threat_level, platform
        `;
        const threatData = await DatabaseManager.query(threatQuery, params);

        // Get top hashtags
        const hashtagQuery = `
            SELECT 
                JSON_UNQUOTE(JSON_EXTRACT(hashtags, '$[*]')) as hashtag,
                COUNT(*) as count
            FROM social_posts 
            WHERE scraped_at >= ? AND JSON_LENGTH(hashtags) > 0${platformFilter}
            GROUP BY hashtag
            ORDER BY count DESC
            LIMIT 20
        `;
        const topHashtags = await DatabaseManager.query(hashtagQuery, params);

        // Get engagement metrics
        const engagementQuery = `
            SELECT 
                platform,
                AVG(like_count) as avg_likes,
                AVG(comment_count) as avg_comments,
                AVG(share_count) as avg_shares,
                SUM(like_count) as total_likes,
                SUM(comment_count) as total_comments,
                SUM(share_count) as total_shares
            FROM social_posts 
            WHERE scraped_at >= ?${platformFilter}
            GROUP BY platform
        `;
        const engagementData = await DatabaseManager.query(engagementQuery, params);

        res.json({
            timeframe,
            startTime,
            totalPosts,
            sentimentDistribution: sentimentData,
            threatLevels: threatData,
            topHashtags,
            engagement: engagementData,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        apiLogger.error('Error getting analytics overview:', error);
        res.status(500).json({
            error: 'Analytics fetch failed',
            message: error.message
        });
    }
});

// GET /api/analytics/sentiment - Get sentiment analysis data
router.get('/sentiment', async (req, res) => {
    try {
        const { 
            timeframe = '24h', 
            platform, 
            groupBy = 'hour',
            keyword 
        } = req.query;

        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

        let sql = `
            SELECT 
                DATE_FORMAT(post_date, 
                    CASE 
                        WHEN ? = 'hour' THEN '%Y-%m-%d %H:00:00'
                        WHEN ? = 'day' THEN '%Y-%m-%d'
                        WHEN ? = 'week' THEN '%Y-%u'
                        ELSE '%Y-%m-%d %H:00:00'
                    END
                ) as time_period,
                AVG(sentiment_score) as avg_sentiment,
                COUNT(*) as post_count,
                SUM(CASE WHEN sentiment_score > 0.1 THEN 1 ELSE 0 END) as positive_count,
                SUM(CASE WHEN sentiment_score < -0.1 THEN 1 ELSE 0 END) as negative_count,
                SUM(CASE WHEN sentiment_score BETWEEN -0.1 AND 0.1 THEN 1 ELSE 0 END) as neutral_count
            FROM social_posts 
            WHERE post_date >= ? AND sentiment_score IS NOT NULL
        `;

        const params = [groupBy, groupBy, groupBy, startTime];

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        if (keyword) {
            sql += ' AND (content LIKE ? OR JSON_SEARCH(hashtags, "one", ?) IS NOT NULL)';
            params.push(`%${keyword}%`, `%${keyword}%`);
        }

        sql += ' GROUP BY time_period ORDER BY time_period';

        const sentimentData = await DatabaseManager.query(sql, params);

        res.json({
            timeframe,
            groupBy,
            platform,
            keyword,
            data: sentimentData,
            summary: {
                totalPosts: sentimentData.reduce((sum, item) => sum + item.post_count, 0),
                avgSentiment: sentimentData.length > 0 
                    ? sentimentData.reduce((sum, item) => sum + item.avg_sentiment, 0) / sentimentData.length 
                    : 0
            }
        });

    } catch (error) {
        apiLogger.error('Error getting sentiment analytics:', error);
        res.status(500).json({
            error: 'Sentiment analytics fetch failed',
            message: error.message
        });
    }
});

// GET /api/analytics/threats - Get threat detection analytics
router.get('/threats', async (req, res) => {
    try {
        const { 
            timeframe = '24h', 
            platform, 
            threatLevel,
            limit = 100 
        } = req.query;

        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

        let sql = `
            SELECT 
                id,
                platform,
                post_id,
                author_username,
                content,
                threat_level,
                sentiment_score,
                like_count,
                comment_count,
                share_count,
                post_date,
                scraped_at
            FROM social_posts 
            WHERE post_date >= ?
        `;

        const params = [startTime];

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        if (threatLevel) {
            sql += ' AND threat_level = ?';
            params.push(threatLevel);
        } else {
            // Only show medium, high, and critical threats by default
            sql += ' AND threat_level IN ("medium", "high", "critical")';
        }

        sql += ' ORDER BY threat_level DESC, post_date DESC LIMIT ?';
        params.push(parseInt(limit));

        const threats = await DatabaseManager.query(sql, params);

        // Get threat level distribution
        const distributionSql = `
            SELECT threat_level, COUNT(*) as count
            FROM social_posts 
            WHERE post_date >= ?${platform ? ' AND platform = ?' : ''}
            GROUP BY threat_level
        `;
        const distributionParams = [startTime];
        if (platform) distributionParams.push(platform);
        
        const distribution = await DatabaseManager.query(distributionSql, distributionParams);

        res.json({
            timeframe,
            platform,
            threatLevel,
            threats,
            distribution,
            summary: {
                totalThreats: threats.length,
                criticalThreats: threats.filter(t => t.threat_level === 'critical').length,
                highThreats: threats.filter(t => t.threat_level === 'high').length,
                mediumThreats: threats.filter(t => t.threat_level === 'medium').length
            }
        });

    } catch (error) {
        apiLogger.error('Error getting threat analytics:', error);
        res.status(500).json({
            error: 'Threat analytics fetch failed',
            message: error.message
        });
    }
});

// GET /api/analytics/hashtags - Get hashtag analytics
router.get('/hashtags', async (req, res) => {
    try {
        const { 
            timeframe = '24h', 
            platform,
            limit = 50 
        } = req.query;

        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

        // This is a simplified version - in production, you'd want to properly parse JSON arrays
        let sql = `
            SELECT 
                TRIM(BOTH '"' FROM JSON_EXTRACT(hashtags, '$[0]')) as hashtag,
                COUNT(*) as count,
                AVG(sentiment_score) as avg_sentiment,
                AVG(like_count + comment_count + share_count) as avg_engagement
            FROM social_posts 
            WHERE post_date >= ? AND JSON_LENGTH(hashtags) > 0
        `;

        const params = [startTime];

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        sql += ' GROUP BY hashtag HAVING count > 1 ORDER BY count DESC LIMIT ?';
        params.push(parseInt(limit));

        const hashtags = await DatabaseManager.query(sql, params);

        res.json({
            timeframe,
            platform,
            hashtags,
            summary: {
                totalHashtags: hashtags.length,
                totalMentions: hashtags.reduce((sum, item) => sum + item.count, 0)
            }
        });

    } catch (error) {
        apiLogger.error('Error getting hashtag analytics:', error);
        res.status(500).json({
            error: 'Hashtag analytics fetch failed',
            message: error.message
        });
    }
});

// GET /api/analytics/users - Get user analytics
router.get('/users', async (req, res) => {
    try {
        const { 
            timeframe = '24h', 
            platform,
            sortBy = 'post_count',
            limit = 50 
        } = req.query;

        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

        const sortOptions = {
            'post_count': 'post_count DESC',
            'engagement': 'total_engagement DESC',
            'sentiment': 'avg_sentiment DESC',
            'threats': 'threat_posts DESC'
        };

        const orderBy = sortOptions[sortBy] || 'post_count DESC';

        let sql = `
            SELECT 
                author_username,
                platform,
                COUNT(*) as post_count,
                AVG(sentiment_score) as avg_sentiment,
                SUM(like_count + comment_count + share_count) as total_engagement,
                AVG(like_count + comment_count + share_count) as avg_engagement,
                SUM(CASE WHEN threat_level IN ('medium', 'high', 'critical') THEN 1 ELSE 0 END) as threat_posts,
                MAX(post_date) as last_post_date
            FROM social_posts 
            WHERE post_date >= ?
        `;

        const params = [startTime];

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        sql += ` GROUP BY author_username, platform ORDER BY ${orderBy} LIMIT ?`;
        params.push(parseInt(limit));

        const users = await DatabaseManager.query(sql, params);

        res.json({
            timeframe,
            platform,
            sortBy,
            users,
            summary: {
                totalUsers: users.length,
                totalPosts: users.reduce((sum, user) => sum + user.post_count, 0),
                totalEngagement: users.reduce((sum, user) => sum + user.total_engagement, 0)
            }
        });

    } catch (error) {
        apiLogger.error('Error getting user analytics:', error);
        res.status(500).json({
            error: 'User analytics fetch failed',
            message: error.message
        });
    }
});

// GET /api/analytics/search - Search posts with analytics
router.get('/search', async (req, res) => {
    try {
        const { 
            query,
            platform,
            timeframe = '7d',
            threatLevel,
            sentiment,
            limit = 100,
            offset = 0
        } = req.query;

        if (!query) {
            return res.status(400).json({
                error: 'Missing query',
                message: 'Search query is required'
            });
        }

        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };
        
        const hours = timeRanges[timeframe] || 24 * 7;
        const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

        let sql = `
            SELECT 
                id, platform, post_id, author_username, content,
                sentiment_score, threat_level, like_count, comment_count,
                share_count, post_date, hashtags, mentions
            FROM social_posts 
            WHERE post_date >= ? AND (
                MATCH(content) AGAINST(? IN NATURAL LANGUAGE MODE) OR
                JSON_SEARCH(hashtags, 'one', ?) IS NOT NULL OR
                JSON_SEARCH(mentions, 'one', ?) IS NOT NULL
            )
        `;

        const params = [startTime, query, `%${query}%`, `%${query}%`];

        if (platform) {
            sql += ' AND platform = ?';
            params.push(platform);
        }

        if (threatLevel) {
            sql += ' AND threat_level = ?';
            params.push(threatLevel);
        }

        if (sentiment) {
            if (sentiment === 'positive') {
                sql += ' AND sentiment_score > 0.1';
            } else if (sentiment === 'negative') {
                sql += ' AND sentiment_score < -0.1';
            } else if (sentiment === 'neutral') {
                sql += ' AND sentiment_score BETWEEN -0.1 AND 0.1';
            }
        }

        sql += ' ORDER BY post_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const posts = await DatabaseManager.query(sql, params);

        res.json({
            query,
            platform,
            timeframe,
            threatLevel,
            sentiment,
            posts,
            count: posts.length,
            pagination: {
                limit: parseInt(limit),
                offset: parseInt(offset)
            }
        });

    } catch (error) {
        apiLogger.error('Error searching posts:', error);
        res.status(500).json({
            error: 'Search failed',
            message: error.message
        });
    }
});

module.exports = router;
