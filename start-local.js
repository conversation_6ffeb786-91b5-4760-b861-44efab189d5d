#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3000;

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Demo data for testing
const demoData = {
    posts: [
        {
            id: 1,
            platform: 'twitter',
            post_id: 'demo_1',
            author_username: 'demo_user_1',
            content: 'Excited about the upcoming Malawi elections! #MalawiElections #Democracy',
            sentiment_score: 0.8,
            threat_level: 'low',
            like_count: 45,
            comment_count: 12,
            share_count: 8,
            post_date: new Date().toISOString(),
            hashtags: ['#MalawiElections', '#Democracy']
        },
        {
            id: 2,
            platform: 'facebook',
            post_id: 'demo_2',
            author_username: 'demo_user_2',
            content: 'We need peaceful elections in Malawi. Violence is not the answer.',
            sentiment_score: 0.3,
            threat_level: 'medium',
            like_count: 23,
            comment_count: 7,
            share_count: 15,
            post_date: new Date(Date.now() - 3600000).toISOString(),
            hashtags: ['#PeacefulElections']
        },
        {
            id: 3,
            platform: 'twitter',
            post_id: 'demo_3',
            author_username: 'demo_user_3',
            content: 'Chakwera and Mutharika debate was very informative. Good to see democratic discourse.',
            sentiment_score: 0.6,
            threat_level: 'low',
            like_count: 67,
            comment_count: 23,
            share_count: 12,
            post_date: new Date(Date.now() - 7200000).toISOString(),
            hashtags: ['#MalawiDebate', '#Democracy']
        }
    ],
    analytics: {
        totalPosts: 3,
        sentimentDistribution: {
            positive: 2,
            neutral: 0,
            negative: 1
        },
        threatLevels: {
            low: 2,
            medium: 1,
            high: 0,
            critical: 0
        },
        topHashtags: [
            { hashtag: '#MalawiElections', count: 1 },
            { hashtag: '#Democracy', count: 2 },
            { hashtag: '#PeacefulElections', count: 1 }
        ]
    }
};

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Social Media Scraper API - Local Demo Mode',
        version: '1.0.0',
        status: 'running',
        mode: 'demo',
        endpoints: {
            health: '/api/health',
            demo_posts: '/api/demo/posts',
            demo_analytics: '/api/demo/analytics'
        },
        note: 'This is running in demo mode with sample data. Configure .env and restart for full functionality.'
    });
});

// Health endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        mode: 'demo',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
            database: { status: 'demo', message: 'Using demo data' },
            cache: { status: 'demo', message: 'Using demo data' },
            scrapers: { status: 'demo', message: 'Using demo data' }
        }
    });
});

// Demo endpoints
app.get('/api/demo/posts', (req, res) => {
    const { platform, limit = 10 } = req.query;
    let posts = demoData.posts;
    
    if (platform) {
        posts = posts.filter(post => post.platform === platform);
    }
    
    posts = posts.slice(0, parseInt(limit));
    
    res.json({
        posts,
        count: posts.length,
        total: demoData.posts.length,
        note: 'This is demo data. Configure API keys for real data.'
    });
});

app.get('/api/demo/analytics', (req, res) => {
    res.json({
        ...demoData.analytics,
        timeframe: '24h',
        note: 'This is demo data. Configure database for real analytics.'
    });
});

// Demo scraping endpoint
app.post('/api/demo/search', (req, res) => {
    const { keyword, platform } = req.body;
    
    // Simulate search results
    const results = demoData.posts.filter(post => 
        post.content.toLowerCase().includes(keyword?.toLowerCase() || '') ||
        post.hashtags.some(tag => tag.toLowerCase().includes(keyword?.toLowerCase() || ''))
    );
    
    res.json({
        keyword,
        platform: platform || 'all',
        posts: results,
        count: results.length,
        note: 'This is a demo search. Configure API keys for real scraping.'
    });
});

// Configuration status
app.get('/api/config/status', (req, res) => {
    const config = {
        database: {
            configured: !!(process.env.DATABASE_HOST && process.env.DATABASE_NAME),
            host: process.env.DATABASE_HOST || 'not configured',
            name: process.env.DATABASE_NAME || 'not configured'
        },
        redis: {
            configured: !!process.env.REDIS_HOST,
            host: process.env.REDIS_HOST || 'not configured'
        },
        apis: {
            twitter: {
                configured: !!(process.env.TWITTER_BEARER_TOKEN || process.env.TWITTER_API_KEY),
                hasBearer: !!process.env.TWITTER_BEARER_TOKEN,
                hasApiKey: !!process.env.TWITTER_API_KEY
            },
            facebook: {
                configured: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
                hasAppId: !!process.env.FACEBOOK_APP_ID,
                hasAppSecret: !!process.env.FACEBOOK_APP_SECRET
            },
            tiktok: {
                configured: !!process.env.TIKTOK_API_KEY,
                hasApiKey: !!process.env.TIKTOK_API_KEY
            }
        }
    };
    
    res.json({
        configuration: config,
        recommendations: [
            !config.database.configured && 'Configure database connection in .env',
            !config.redis.configured && 'Configure Redis connection in .env',
            !config.apis.twitter.configured && 'Add Twitter API keys to .env for Twitter scraping',
            !config.apis.facebook.configured && 'Add Facebook API keys to .env for Facebook scraping'
        ].filter(Boolean)
    });
});

// Setup instructions
app.get('/api/setup', (req, res) => {
    res.json({
        title: 'Social Media Scraper Setup Instructions',
        steps: [
            {
                step: 1,
                title: 'Install Dependencies',
                description: 'MySQL and Redis are required for full functionality',
                commands: [
                    'Install MySQL: https://dev.mysql.com/downloads/',
                    'Install Redis: https://redis.io/download',
                    'Create database: CREATE DATABASE social_media_monitoring;'
                ]
            },
            {
                step: 2,
                title: 'Configure Environment',
                description: 'Edit .env file with your settings',
                file: '.env',
                required: [
                    'DATABASE_PASSWORD=your_mysql_password',
                    'TWITTER_BEARER_TOKEN=your_twitter_token',
                    'FACEBOOK_APP_ID=your_facebook_app_id'
                ]
            },
            {
                step: 3,
                title: 'Get API Keys',
                description: 'Register for social media APIs',
                links: [
                    'Twitter: https://developer.twitter.com/',
                    'Facebook: https://developers.facebook.com/'
                ]
            },
            {
                step: 4,
                title: 'Start Full Application',
                description: 'Run the complete system',
                command: 'npm start'
            }
        ],
        currentMode: 'demo',
        note: 'You are currently running in demo mode with sample data.'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        message: `The requested endpoint ${req.originalUrl} does not exist`,
        availableEndpoints: [
            'GET /',
            'GET /api/health',
            'GET /api/demo/posts',
            'GET /api/demo/analytics',
            'POST /api/demo/search',
            'GET /api/config/status',
            'GET /api/setup'
        ]
    });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('Error:', error);
    res.status(500).json({
        error: 'Server Error',
        message: error.message,
        mode: 'demo'
    });
});

// Start server
app.listen(port, () => {
    console.log(`🚀 Social Media Scraper Demo running on http://localhost:${port}`);
    console.log(`📊 Demo data available at http://localhost:${port}/api/demo/posts`);
    console.log(`⚙️  Configuration status at http://localhost:${port}/api/config/status`);
    console.log(`📋 Setup instructions at http://localhost:${port}/api/setup`);
    console.log(`\n💡 This is demo mode. Configure .env and use 'npm start' for full functionality.`);
});

module.exports = app;
